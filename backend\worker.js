/**
 * ========== 后端改造说明 ==========
 *
 * 本系统支持两种运行模式：
 * 1. 【KV存储模式】- 原有模式，使用Cloudflare KV存储用户数据
 * 2. 【主后端API模式】- 新模式，用户认证、配额管理等功能迁移到主后端API
 *
 * 环境变量配置：
 * - MAIN_BACKEND_BASE: 主后端API的基础URL（如：https://api.example.com）
 *   - 如果设置此变量，系统将启用主后端API模式
 *   - 如果未设置或为空，系统将使用KV存储模式作为降级方案
 * - B_BACKEND_API_TOKEN: B后端API的访问令牌（用于配额管理）
 *   - 格式：B_Backend_API_2024_SecureToken_X9k2#mP8$vL3nQ5@jR7wY4*tZ6
 *   - 用于调用B后端的配额预检查和配额扣除接口
 * - QUOTA_FALLBACK_ENABLED: 已废弃的环境变量（保留用于向后兼容）
 *   - 新的配额管理策略：
 *     * 配额验证（checkVip）：智能模式选择
 *       - 如果MAIN_BACKEND_BASE + B_BACKEND_API_TOKEN都配置：采用严格模式，API失败时任务失败
 *       - 如果未配置或配置不完整：自动降级到KV存储模式
 *     * 配额扣除（updateUserUsage）：始终允许降级到KV存储，确保用户体验
 *
 * 迁移的功能：
 * - 用户登录 (/api/auth/login)
 * - 用户注册 (/api/auth/send-verification, /api/auth/verify-email)
 * - 密码管理 (/api/auth/change-password, /api/auth/forgot-password, /api/auth/reset-password)
 * - 配额查询 (/api/user/quota)
 * - 卡密激活 (/api/card/use)
 *
 * 保留的功能（继续使用KV存储）：
 * - TTS任务处理和状态管理
 * - 音频文件存储（R2）
 * - WebSocket连接管理
 * - 任务重试机制
 */

// ========== 配置部分 ==========
const getAuthConfig = (env) => ({
  JWT_SECRET: env.JWT_SECRET,
  ACCESS_TOKEN_EXPIRE: 2 * 60 * 60, // 2小时
  REFRESH_TOKEN_EXPIRE: 7 * 24 * 60 * 60, // 7天
  SALT_ROUNDS: 10
});

// ========== 增强的统一日志记录器 ==========
/**
 * 获取日志配置
 * @param {object} env - Cloudflare环境变量
 */
function getLogConfig(env) {
  return {
    // 基础配置
    DEBUG_ENABLED: env.DEBUG === 'true' || env.DEBUG === true,
    LOG_LEVEL: env.LOG_LEVEL || 'INFO', // DEBUG, INFO, WARN, ERROR

    // 性能配置
    ENABLE_PERFORMANCE_LOGS: env.ENABLE_PERFORMANCE_LOGS !== 'false',
    ENABLE_FLOW_TRACKING: env.ENABLE_FLOW_TRACKING !== 'false',

    // 代理相关日志
    ENABLE_PROXY_DEBUG: env.ENABLE_PROXY_DEBUG === 'true',
    ENABLE_PROXY_PERFORMANCE: env.ENABLE_PROXY_PERFORMANCE !== 'false',

    // 错误追踪
    ENABLE_ERROR_STACK: env.ENABLE_ERROR_STACK !== 'false',
    MAX_STACK_LENGTH: parseInt(env.MAX_STACK_LENGTH || '500'),

    // 数据脱敏
    ENABLE_DATA_SANITIZATION: env.ENABLE_DATA_SANITIZATION !== 'false',
    MAX_TEXT_PREVIEW: parseInt(env.MAX_TEXT_PREVIEW || '100')
  };
}

/**
 * 数据脱敏函数
 * @param {any} data - 需要脱敏的数据
 * @param {object} config - 日志配置
 */
function sanitizeLogData(data, config) {
  if (!config.ENABLE_DATA_SANITIZATION) return data;

  const sanitized = { ...data };

  // 脱敏敏感字段
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });

  // 限制文本预览长度
  if (sanitized.textPreview && sanitized.textPreview.length > config.MAX_TEXT_PREVIEW) {
    sanitized.textPreview = sanitized.textPreview.substring(0, config.MAX_TEXT_PREVIEW) + '...';
  }

  return sanitized;
}

/**
 * 创建一个带上下文的增强日志记录器
 * @param {object} env - Cloudflare环境变量
 */
function createLogger(env) {
  const config = getLogConfig(env);

  // 日志级别优先级
  const LOG_LEVELS = { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 };
  const currentLevel = LOG_LEVELS[config.LOG_LEVEL] || LOG_LEVELS.INFO;

  const log = (level, message, data = {}, context = {}) => {
    // 检查日志级别
    if (LOG_LEVELS[level] < currentLevel) {
      return;
    }

    // 特殊处理DEBUG级别
    if (level === 'DEBUG' && !config.DEBUG_ENABLED) {
      return;
    }

    const timestamp = new Date().toISOString();
    const username = context.username || 'system';
    const taskId = context.taskId || 'N/A';

    // 【增强】构建更丰富的上下文信息
    let contextParts = `[user:${username}] [task:${taskId}]`;
    if (context.chunkIndex) {
      contextParts += ` [chunk:${context.chunkIndex}]`;
    }
    if (context.speakerIndex) {
      contextParts += ` [speaker:${context.speakerIndex}]`;
    }
    if (context.operation) {
      contextParts += ` [op:${context.operation}]`;
    }

    // 【新增】性能追踪
    if (context.duration !== undefined) {
      contextParts += ` [duration:${context.duration}ms]`;
    }

    // 格式化输出
    const logString = `[${level}] [${timestamp}] ${contextParts} - ${message}`;

    // 数据脱敏
    const sanitizedData = sanitizeLogData(data, config);

    if (Object.keys(sanitizedData).length > 0) {
      console.log(logString, sanitizedData);
    } else {
      console.log(logString);
    }
  };

  return {
    debug: (message, data, context) => log('DEBUG', message, data, context),
    info: (message, data, context) => log('INFO', message, data, context),
    warn: (message, data, context) => log('WARN', message, data, context),
    error: (error, context, additionalData = {}) => {
      const message = error.message || 'Unknown error';
      const data = {
        ...additionalData,
        error: message,
        errorType: error.constructor?.name || 'Error',
        ...(config.ENABLE_ERROR_STACK && error.stack && {
          stack: error.stack.substring(0, config.MAX_STACK_LENGTH)
        })
      };
      log('ERROR', message, data, context);
    },

    // 【新增】专用的性能日志方法
    perf: (operation, duration, context = {}, additionalData = {}) => {
      if (!config.ENABLE_PERFORMANCE_LOGS) return;

      const perfContext = { ...context, operation, duration };
      log('INFO', `Performance: ${operation}`, {
        duration: `${duration}ms`,
        ...additionalData
      }, perfContext);
    },

    // 【新增】专用的流程追踪方法
    flow: (step, status, context = {}, additionalData = {}) => {
      if (!config.ENABLE_FLOW_TRACKING) return;

      const flowContext = { ...context, operation: 'flow' };
      log('INFO', `Flow: ${step} - ${status}`, additionalData, flowContext);
    }
  };
}

/**
 * 在env对象中注入日志上下文和记录器
 * @param {object} env - 原始环境对象
 * @param {object} logContext - 日志上下文 {username, taskId}
 * @returns {object} 增强后的环境对象
 */
function enhanceEnvWithLogging(env, logContext = {}) {
  // 创建logger实例
  const logger = createLogger(env);

  // 在env中注入日志相关属性
  env._logContext = logContext;
  env._logger = logger;

  // 提供便捷的日志方法，自动使用上下文
  env._log = {
    debug: (message, data = {}) => logger.debug(message, data, env._logContext),
    info: (message, data = {}) => logger.info(message, data, env._logContext),
    warn: (message, data = {}) => logger.warn(message, data, env._logContext),
    error: (error, additionalData = {}) => logger.error(error, env._logContext, additionalData),
    // 【关键修复】添加缺少的 perf 和 flow 方法
    perf: (operation, duration, additionalData = {}) => logger.perf(operation, duration, env._logContext, additionalData),
    flow: (step, status, additionalData = {}) => logger.flow(step, status, env._logContext, additionalData)
  };

  return env;
}

const getSESConfig = (env) => ({
  TENCENT_SECRET_ID: env.TENCENT_SECRET_ID,
  TENCENT_SECRET_KEY: env.TENCENT_SECRET_KEY,
  SES_REGION: env.SES_REGION || 'ap-guangzhou',
  FROM_EMAIL: env.FROM_EMAIL,
  FROM_EMAIL_NAME: env.FROM_EMAIL_NAME || '验证服务',
  VERIFICATION_TEMPLATE_ID: env.VERIFICATION_TEMPLATE_ID
});

// R2直链下载配置
const R2_DIRECT_DOWNLOAD_CONFIG = {
  DOMAIN: 'r2-assets.aispeak.top',
  PATH_PREFIX: 'audios',
  // 生成完整的R2直链URL
  generateUrl: (taskId) => `https://${R2_DIRECT_DOWNLOAD_CONFIG.DOMAIN}/${R2_DIRECT_DOWNLOAD_CONFIG.PATH_PREFIX}/${taskId}.mp3`
};

// 进度消息配置
const getProgressConfig = (env) => ({
  // 控制是否发送详细进度消息的开关
  ENABLE_PROGRESS_MESSAGES: env.ENABLE_PROGRESS_MESSAGES === 'true' || env.ENABLE_PROGRESS_MESSAGES === true,
  // 可以根据需要添加更多进度相关配置
  ENABLE_DEBUG_PROGRESS: env.DEBUG === 'true' || env.DEBUG === true
});

// 【升级版】TTS 代理配置 - 支持多个备用代理 + 完全向后兼容
const getTTSProxyConfig = (env) => {
  // 【核心升级】智能解析代理URL配置，支持新旧两种格式
  let proxyUrls = [];

  // 优先使用新的多URL配置 TTS_PROXY_URLS
  if (env.TTS_PROXY_URLS) {
    proxyUrls = env.TTS_PROXY_URLS
      .split(',')
      .map(url => url.trim())
      .filter(Boolean); // 移除空项
  }
  // 向后兼容：如果没有新配置，使用旧的单URL配置
  else if (env.TTS_PROXY_URL) {
    proxyUrls = [env.TTS_PROXY_URL];
  }

  return {
    // 基础代理配置
    ENABLE_TTS_PROXY: env.ENABLE_TTS_PROXY === 'true' || env.ENABLE_TTS_PROXY === true,

    // 【新增】多代理URL列表（主要配置）
    TTS_PROXY_URLS: proxyUrls,

    // 【保留】单一代理URL（向后兼容，从列表中取第一个）
    TTS_PROXY_URL: proxyUrls.length > 0 ? proxyUrls[0] : null,

    TTS_PROXY_SECRET: env.TTS_PROXY_SECRET || null, // 代理认证密钥

    // 代理策略配置
    TTS_PROXY_MODE: env.TTS_PROXY_MODE || 'fallback', // 'direct', 'proxy', 'balanced', 'fallback'
    TTS_PROXY_TIMEOUT: parseInt(env.TTS_PROXY_TIMEOUT || '45000'), // 代理请求超时时间（毫秒）- 从30秒提升到45秒
    TTS_PROXY_RETRY_COUNT: parseInt(env.TTS_PROXY_RETRY_COUNT || '2'), // 代理重试次数

    // 【新增】健康检查配置
    TTS_HEALTH_CHECK_ENABLED: env.TTS_HEALTH_CHECK_ENABLED !== 'false', // 默认启用健康检查
    TTS_HEALTH_CHECK_TIMEOUT: parseInt(env.TTS_HEALTH_CHECK_TIMEOUT || '3000'), // 实时健康检查超时: 3秒
    TTS_HEALTH_CHECK_RETRIES: parseInt(env.TTS_HEALTH_CHECK_RETRIES || '2'), // 健康检查重试次数: 2次
    TTS_HEALTH_CHECK_INTERVAL: parseInt(env.TTS_HEALTH_CHECK_INTERVAL || '2000'), // 健康检查重试间隔: 2秒
    TTS_HEALTHY_PROXY_TIMEOUT: parseInt(env.TTS_HEALTHY_PROXY_TIMEOUT || '60000'), // 健康代理超时: 60秒

    // 负载均衡配置（当模式为 'balanced' 时使用）
    TTS_PROXY_BALANCE_RATIO: parseFloat(env.TTS_PROXY_BALANCE_RATIO || '0.3'), // 30% 流量走代理

    // 故障转移配置
    TTS_FALLBACK_THRESHOLD: parseInt(env.TTS_FALLBACK_THRESHOLD || '2'), // 连续失败N次后启用预防性代理
    TTS_FALLBACK_WINDOW: parseInt(env.TTS_FALLBACK_WINDOW || '300'), // 故障检测时间窗口（秒）

    // 【新增】集群级重试和退避配置
    TTS_CLUSTER_RETRY_COUNT: parseInt(env.TTS_CLUSTER_RETRY_COUNT || '3'), // 集群级重试次数
    TTS_CLUSTER_MAX_DELAY: parseInt(env.TTS_CLUSTER_MAX_DELAY || '8000'), // 集群重试最大延迟（毫秒）
    TTS_SINGLE_MAX_DELAY: parseInt(env.TTS_SINGLE_MAX_DELAY || '5000'), // 单代理重试最大延迟（毫秒）
    TTS_DIRECT_MAX_DELAY: parseInt(env.TTS_DIRECT_MAX_DELAY || '8000'), // 直连重试最大延迟（毫秒）
    TTS_ENABLE_BACKOFF: env.TTS_ENABLE_BACKOFF !== 'false', // 默认启用指数退避

    // 【新增】代理选择策略配置
    /**
     * 代理选择策略
     * 'sequential': 严格按配置顺序尝试 (默认, 故障转移)
     * 'random': 每次随机打乱顺序尝试 (负载均衡 + 故障转移)
     */
    TTS_PROXY_SELECTION_STRATEGY: env.TTS_PROXY_SELECTION_STRATEGY || 'sequential',

    // 调试和监控
    ENABLE_PROXY_STATS: env.ENABLE_PROXY_STATS !== 'false', // 默认启用代理统计
    ENABLE_PROXY_DEBUG: env.ENABLE_PROXY_DEBUG === 'true' || env.DEBUG === 'true'
  };
};

// 【新增】任务级重试配置 - 基于策略切换的渐进式重试
const getTaskRetryConfig = (env) => ({
  // 任务级重试开关
  ENABLE_TASK_RETRY: env.ENABLE_TASK_RETRY !== 'false', // 默认启用

  // 重试次数配置（保守设置）
  MAX_TASK_RETRIES: parseInt(env.MAX_TASK_RETRIES || '2'), // 最多重试2次（总共尝试3次）

  // 重试延迟配置（渐进式）
  TASK_RETRY_DELAYS: [
    parseInt(env.TASK_RETRY_DELAY_1 || '6000'),  // 第一次重试：6秒
    parseInt(env.TASK_RETRY_DELAY_2 || '12000')  // 第二次重试：12秒
  ],

  // 绝对超时限制（防止用户无限等待）
  TASK_ABSOLUTE_TIMEOUT: parseInt(env.TASK_ABSOLUTE_TIMEOUT || '600000'), // 10分钟绝对上限

  // 调试和监控
  ENABLE_TASK_RETRY_DEBUG: env.ENABLE_TASK_RETRY_DEBUG === 'true' || env.DEBUG === 'true'
});

// 【新增】智能超时配置 - 支持环境变量配置和动态调整
const getSmartTimeoutConfig = (env) => ({
  // 基础超时配置（可通过环境变量调整）
  INIT_TIMEOUT: parseInt(env.TTS_INIT_TIMEOUT || '30000'), // 初始化超时：30秒
  TEXT_PROCESSING_TIMEOUT: parseInt(env.TTS_TEXT_PROCESSING_TIMEOUT || '60000'), // 文本处理：1分钟
  AUDIO_MERGING_TIMEOUT: parseInt(env.TTS_AUDIO_MERGING_TIMEOUT || '120000'), // 音频合并：2分钟
  R2_STORAGE_TIMEOUT: parseInt(env.TTS_R2_STORAGE_TIMEOUT || '180000'), // R2存储：3分钟
  DEFAULT_TIMEOUT: parseInt(env.TTS_DEFAULT_TIMEOUT || '300000'), // 默认：5分钟

  // 音频生成的智能超时配置
  CHUNK_BASE_TIMEOUT: parseInt(env.TTS_CHUNK_TIMEOUT || '40000'), // 每chunk基础超时：40秒（从30秒提升）
  MIN_AUDIO_TIMEOUT: parseInt(env.TTS_MIN_TIMEOUT || '120000'), // 音频生成最少：2分钟
  MAX_AUDIO_TIMEOUT: parseInt(env.TTS_MAX_TIMEOUT || '900000'), // 音频生成最多：15分钟（从10分钟提升）

  // 【新增】WebSocket心跳配置
  HEARTBEAT_INTERVAL: parseInt(env.TTS_HEARTBEAT_INTERVAL || '30000'), // 心跳间隔：30秒
  PROGRESS_UPDATE_INTERVAL: parseInt(env.TTS_PROGRESS_INTERVAL || '15000'), // 进度更新间隔：15秒

  // 复杂度调整因子
  ENABLE_COMPLEXITY_ADJUSTMENT: env.TTS_ENABLE_COMPLEXITY_ADJUSTMENT !== 'false', // 默认启用复杂度调整
  LARGE_CHUNK_THRESHOLD: parseInt(env.TTS_LARGE_CHUNK_THRESHOLD || '10'), // 大量chunk阈值
  HUGE_CHUNK_THRESHOLD: parseInt(env.TTS_HUGE_CHUNK_THRESHOLD || '20'), // 超大量chunk阈值
  LARGE_TEXT_THRESHOLD: parseInt(env.TTS_LARGE_TEXT_THRESHOLD || '5000'), // 大文本字符数阈值
  HUGE_TEXT_THRESHOLD: parseInt(env.TTS_HUGE_TEXT_THRESHOLD || '10000'), // 超大文本字符数阈值

  // 调试开关
  ENABLE_TIMEOUT_DEBUG: env.TTS_ENABLE_TIMEOUT_DEBUG === 'true' || env.DEBUG === 'true'
});

/**
 * 【新增】智能计算音频生成阶段的超时时间
 * 根据任务复杂度、重试次数等因素动态调整超时时间
 * @param {object} taskStatus - 任务状态对象
 * @param {object} env - 环境对象
 * @returns {object} 包含超时时间和计算详情的对象
 */
function calculateAudioGenerationTimeout(taskStatus, env) {
  const timeoutConfig = getSmartTimeoutConfig(env);

  // 1. 基础参数
  const chunkCount = taskStatus.totalChunks || 1;
  const totalChars = taskStatus.totalChars || 0;
  const taskType = taskStatus.taskType || 'single';
  const retryAttempt = taskStatus.retryAttempt || 1;

  // 2. 计算复杂度因子
  let complexityFactor = 1.0;
  const complexityDetails = [];

  if (timeoutConfig.ENABLE_COMPLEXITY_ADJUSTMENT) {
    // 根据chunk数量调整
    if (chunkCount > timeoutConfig.HUGE_CHUNK_THRESHOLD) {
      complexityFactor += 0.5;
      complexityDetails.push(`huge_chunks(${chunkCount}): +0.5`);
    } else if (chunkCount > timeoutConfig.LARGE_CHUNK_THRESHOLD) {
      complexityFactor += 0.3;
      complexityDetails.push(`large_chunks(${chunkCount}): +0.3`);
    }

    // 根据总字符数调整
    if (totalChars > timeoutConfig.HUGE_TEXT_THRESHOLD) {
      complexityFactor += 0.3;
      complexityDetails.push(`huge_text(${totalChars}): +0.3`);
    } else if (totalChars > timeoutConfig.LARGE_TEXT_THRESHOLD) {
      complexityFactor += 0.2;
      complexityDetails.push(`large_text(${totalChars}): +0.2`);
    }

    // 根据任务类型调整
    if (taskType === 'dialogue') {
      complexityFactor += 0.3;
      complexityDetails.push('dialogue_task: +0.3');
    }

    // 根据重试次数调整（重试时给更多时间）
    if (retryAttempt > 1) {
      const retryBonus = Math.min((retryAttempt - 1) * 0.5, 1.0);
      complexityFactor += retryBonus;
      complexityDetails.push(`retry_attempt(${retryAttempt}): +${retryBonus}`);
    }
  }

  // 限制复杂度因子的最大值
  complexityFactor = Math.min(complexityFactor, 2.5);

  // 3. 计算最终超时时间
  const adjustedChunkTimeout = timeoutConfig.CHUNK_BASE_TIMEOUT * complexityFactor;
  const estimatedTime = Math.max(
    timeoutConfig.MIN_AUDIO_TIMEOUT,
    chunkCount * adjustedChunkTimeout
  );
  const finalTimeout = Math.min(estimatedTime, timeoutConfig.MAX_AUDIO_TIMEOUT);

  // 4. 构建详细信息（用于调试和日志）
  const calculationDetails = {
    chunkCount,
    totalChars,
    taskType,
    retryAttempt,
    baseChunkTimeout: timeoutConfig.CHUNK_BASE_TIMEOUT,
    complexityFactor: Math.round(complexityFactor * 100) / 100,
    complexityDetails,
    adjustedChunkTimeout: Math.round(adjustedChunkTimeout),
    estimatedTime: Math.round(estimatedTime),
    finalTimeout: Math.round(finalTimeout),
    minTimeout: timeoutConfig.MIN_AUDIO_TIMEOUT,
    maxTimeout: timeoutConfig.MAX_AUDIO_TIMEOUT
  };

  return {
    timeoutMs: finalTimeout,
    details: calculationDetails
  };
}

// ========== DO位置提示配置 ==========
// 【优先级策略】使用分层级的路由优先级，以保证100%兼容性
const DO_LOCATION_PRIORITY_TIERS = [
  // Tier 1: 最高优先级 - 地区
  ['enam'],
  // Tier 2: 次高优先级 - 地区 (随机选择)
  ['wnam', 'eeur'],
  // Tier 3: 最低优先级/故障转移 - 地区 (随机选择)
  ['weur', 'apac']
];

/**
 * 【最终版 - 分层优先级】根据预设的优先级层次获取DO位置提示。
 * @param {object} env - Cloudflare环境变量
 * @param {string[]} excludeLocations - 要动态排除的故障区域列表
 * @returns {string|undefined} 根据优先级策略选择的数据中心代码
 */
function getRandomLocationHint(env, excludeLocations = []) {
  // 检查是否启用DO位置提示功能 (此部分逻辑不变)
  const enableLocationHint = env.ENABLE_DO_LOCATION_HINT !== 'false';
  if (!enableLocationHint) {
    if (env.DEBUG) console.log('[DO-ROUTING] Location hint disabled by environment variable');
    return undefined;
  }

  // 【新增】合并任务级重试的排除位置
  const taskRetryExcludes = env._TASK_RETRY_EXCLUDE_LOCATIONS || [];
  const allExcludeLocations = [...excludeLocations, ...taskRetryExcludes];

  if (env.DEBUG) console.log(`[DO-ROUTING] Starting priority-based location selection. Excluding: [${allExcludeLocations.join(', ')}]`);

  // 1. 遍历所有优先级层级
  for (let i = 0; i < DO_LOCATION_PRIORITY_TIERS.length; i++) {
    const tier = DO_LOCATION_PRIORITY_TIERS[i];
    const tierName = i === 0 ? 'Highest (APAC)' : i === 1 ? 'Secondary (Europe)' : 'Failover (North America)';

    // 2. 在当前层级中，找出所有"健康"的区域
    // 即：不在 `allExcludeLocations` 列表中的区域
    const availableHintsInTier = tier.filter(hint => !allExcludeLocations.includes(hint));

    // 3. 如果当前层级有可用的健康区域
    if (availableHintsInTier.length > 0) {
      // 从这些健康区域中随机选择一个
      const selectedHint = availableHintsInTier[Math.floor(Math.random() * availableHintsInTier.length)];

      if (env.DEBUG) {
        console.log(`[DO-ROUTING] ✅ Success! Selected hint '${selectedHint}' from Priority Tier ${i+1} (${tierName}).`);
      }

      // 找到了就立即返回，不再继续尝试更低的优先级
      return selectedHint;
    } else {
      // 如果当前层级没有可用区域 (要么是空的，要么都被排除了)
      if (env.DEBUG) {
        console.log(`[DO-ROUTING] ⚠️ No available hints in Priority Tier ${i+1} (${tierName}). Trying next tier...`);
      }
    }
  }

  // 4. 【兜底逻辑】如果所有层级的所有区域都被排除了
  console.warn('[DO-ROUTING] CRITICAL: All location hints in all priority tiers were excluded. Falling back to default Cloudflare routing.');
  // 返回 undefined，让 Cloudflare 使用其默认的"就近"逻辑
  // 这是最安全的做法，保证服务可用性。
  return undefined;
}

/**
 * 【新增】任务级重试策略定义
 * 基于重试次数返回相应的执行策略配置
 */
const TASK_RETRY_STRATEGIES = {
  // 第一次尝试：标准模式（直连+代理故障转移）
  1: {
    name: 'STANDARD',
    description: '标准模式（直连+代理故障转移）',
    proxyMode: 'fallback',
    excludeLocations: [],
    userMessage: '正在处理您的请求...'
  },

  // 第二次尝试：代理优先模式 + 数据中心提示
  2: {
    name: 'PROXY_PREFERRED',
    description: '代理优先模式 + 数据中心提示',
    proxyMode: 'proxy',
    excludeLocations: [], // 将在运行时动态设置
    userMessage: '遇到临时问题，正在尝试备用线路...'
  },

  // 第三次尝试：纯代理模式 + 不同数据中心
  3: {
    name: 'PROXY_ONLY',
    description: '纯代理模式 + 不同数据中心',
    proxyMode: 'proxy_only',
    excludeLocations: [], // 将在运行时动态设置
    userMessage: '正在切换到更稳定的线路，请稍候...'
  }
};

/**
 * 【新增】获取指定重试次数的策略配置
 * @param {number} attempt - 当前尝试次数（1, 2, 3...）
 * @param {string[]} failedLocations - 已失败的数据中心位置列表
 * @returns {object} 策略配置对象
 */
function getRetryStrategy(attempt, failedLocations = []) {
  const strategy = TASK_RETRY_STRATEGIES[attempt] || TASK_RETRY_STRATEGIES[3]; // 默认使用最后一个策略

  // 动态设置排除的数据中心位置
  return {
    ...strategy,
    excludeLocations: [...failedLocations] // 复制数组，避免修改原始配置
  };
}

/**
 * 【新增】应用重试策略到环境配置
 * 通过修改env对象来影响后续的代理和数据中心选择
 * @param {object} env - 环境对象
 * @param {object} strategy - 重试策略配置
 * @returns {object} 修改后的环境对象
 */
function applyRetryStrategy(env, strategy) {
  // 创建环境对象的副本，避免修改原始对象
  const modifiedEnv = { ...env };

  // 临时修改代理模式
  modifiedEnv.TTS_PROXY_MODE = strategy.proxyMode;

  // 如果有排除的数据中心位置，将其传递给位置提示函数
  // 注意：这里我们通过特殊属性传递，getRandomLocationHint函数会读取它
  modifiedEnv._TASK_RETRY_EXCLUDE_LOCATIONS = strategy.excludeLocations;

  return modifiedEnv;
}

/**
 * 【新增】计算重试延迟时间
 * @param {number} attempt - 重试次数（1, 2, 3...）
 * @param {object} retryConfig - 重试配置
 * @returns {number} 延迟时间（毫秒）
 */
function calculateRetryDelay(attempt, retryConfig) {
  const delays = retryConfig.TASK_RETRY_DELAYS;
  const index = attempt - 2; // attempt=2对应index=0，attempt=3对应index=1

  if (index >= 0 && index < delays.length) {
    return delays[index];
  }

  // 如果超出配置范围，使用最后一个延迟值
  return delays[delays.length - 1] || 12000;
}

/**
 * 【新增】检测是否为内容违规错误（不可重试）
 * @param {number} status - HTTP状态码
 * @param {object} errorData - 解析后的错误数据
 * @param {string} errorMessage - 错误消息
 * @returns {boolean} 是否为内容违规错误
 */
function isContentViolationError(status, errorData, errorMessage) {
  // 1. 必须是403状态码
  if (status !== 403) {
    return false;
  }

  // 2. 检查detail.status字段
  if (errorData?.detail?.status === 'content_against_policy') {
    return true;
  }

  // 3. 检查特定的违规消息
  const violationMessage = "We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.";
  if (errorMessage && errorMessage.includes(violationMessage)) {
    return true;
  }

  // 4. 检查detail.message字段
  if (errorData?.detail?.message && errorData.detail.message.includes(violationMessage)) {
    return true;
  }

  // 5. 检查其他可能的违规关键词
  const violationKeywords = ["violate our Terms", "content_against_policy", "content policy violation"];
  const lowerErrorMessage = errorMessage?.toLowerCase() || '';
  if (violationKeywords.some(keyword => lowerErrorMessage.includes(keyword.toLowerCase()))) {
    return true;
  }

  return false;
}

/**
 * 【增强】检测是否为数据中心级别的可重试错误
 * 新增内容违规检测，确保违规内容不会被重试
 * @param {Error} error - 错误对象
 * @param {number} status - HTTP状态码
 * @param {object} originalErrorData - 原始错误数据对象
 * @returns {boolean} 是否为可重试的数据中心错误
 */
function isDataCenterRetryableError(error, status, originalErrorData = null) {
  // 【新增】优先检查是否为内容违规错误
  if (isContentViolationError(status, originalErrorData, error.message)) {
    return false; // 内容违规错误绝对不可重试
  }
  // 1. HTTP 429 (Too Many Requests) - 明确的配额限制
  if (status === 429) {
    return true;
  }

  // 2. HTTP 503 (Service Unavailable) - 服务暂时不可用
  if (status === 503) {
    return true;
  }

  // 3. 【新增】HTTP 401 配额相关错误
  if (status === 401) {
    // 检查是否是配额相关的401错误
    if (originalErrorData?.detail?.status === 'quota_exceeded') {
      return true;
    }
  }

  // 4. 检查错误消息中的关键词
  const errorMessage = error.message?.toLowerCase() || '';
  const retryableKeywords = [
    'quota',
    'quota_exceeded',
    'rate limit',
    'too many requests',
    'service unavailable',
    'temporarily unavailable',
    'capacity',
    'overloaded',
    'reached the limit'
  ];

  const isRetryableByMessage = retryableKeywords.some(keyword =>
    errorMessage.includes(keyword)
  );

  if (isRetryableByMessage) {
    return true;
  }

  // 5. 【新增】检查原始错误数据中的状态字段
  if (originalErrorData?.detail?.status) {
    const detailStatus = originalErrorData.detail.status.toLowerCase();
    const retryableStatuses = ['quota_exceeded', 'rate_limited', 'capacity_exceeded'];
    if (retryableStatuses.includes(detailStatus)) {
      return true;
    }
  }

  // 6. 网络级别错误（可能是数据中心网络问题）
  if (error.name === 'TypeError' && errorMessage.includes('fetch')) {
    return true;
  }

  // 7. 超时错误
  if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
    return true;
  }

  return false;
}

// ========== Voice ID映射缓存 ==========
// 全局缓存变量，用于缓存从KV获取的voiceIdMapping
let voiceIdMappingCache = null;
let cacheExpireTime = 0;
const CACHE_TTL_MS = 5 * 60 * 1000; // 缓存5分钟

/**
 * 从KV获取并缓存Voice ID映射
 * @param {object} env - Cloudflare环境变量
 * @returns {Promise<object>}
 */
async function getVoiceIdMapping(env) {
  const now = Date.now();

  // 1. 检查内存缓存是否有效
  if (voiceIdMappingCache && now < cacheExpireTime) {
    if (env.DEBUG) {
      console.log('[CACHE] Hit memory cache for voiceIdMapping.');
    }
    return voiceIdMappingCache;
  }

  // 2. 缓存无效，从KV获取
  try {
    if (env.DEBUG) {
      console.log('[KV-FETCH] Fetching voiceIdMapping from KV store.');
    }

    const mappingJson = await env.VOICE_MAPPINGS.get("voices_v1", {
      type: "text",
      cacheTtl: 60 // CF边缘缓存60秒
    });

    if (!mappingJson) {
      console.error('CRITICAL: voiceIdMapping not found in KV namespace "VOICE_MAPPINGS" with key "voices_v1"');
      return {};
    }

    const mapping = JSON.parse(mappingJson);

    // 3. 更新内存缓存和过期时间
    voiceIdMappingCache = mapping;
    cacheExpireTime = now + CACHE_TTL_MS;

    if (env.DEBUG) {
      console.log(`[CACHE] Updated memory cache for voiceIdMapping. Next refresh in ${CACHE_TTL_MS / 1000}s.`);
    }

    return mapping;
  } catch (error) {
    console.error('Failed to get or parse voiceIdMapping from KV:', error);
    // 在出错时，如果缓存中还有旧数据，返回旧数据保证服务韧性
    if (voiceIdMappingCache) {
      console.warn('Returning stale voiceIdMapping cache due to KV fetch error.');
      return voiceIdMappingCache;
    }
    return {}; // 最终回退
  }
}

/**
 * 根据声音名称获取其对应的Voice ID
 * 封装了查找逻辑和回退机制
 * @param {string} voiceName - 要查找的声音名称 (e.g., "Adam")
 * @param {object} env - Cloudflare环境变量
 * @returns {Promise<string>} - 返回找到的Voice ID，如果找不到则返回原始名称
 */
async function getVoiceId(voiceName, env) {
  const mapping = await getVoiceIdMapping(env);
  // 如果在映射中找到，则返回ID；否则，返回原始输入（假定它本身就是ID）
  return mapping[voiceName] || voiceName;
}

// 【已迁移到KV】硬编码的voiceIdMapping已迁移到KV存储
// 现在通过getVoiceIdMapping()和getVoiceId()函数动态获取

// 生成基于年月日时分秒的文件名
function generateDateBasedFilename() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `tts_${year}${month}${day}_${hours}${minutes}${seconds}.mp3`;
}

// 【新增】获取下个月第一天的时间戳，用于月度重置
function getNextMonthResetTimestamp() {
  const now = new Date();
  // 设置为下个月的第一天的 0 点 0 分 0 秒
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  return nextMonth.getTime();
}

// 【新增】检查管理员权限
async function checkAdminPermission(username, env) {
  // 从环境变量获取管理员列表，默认为空数组
  const adminList = env.ADMIN_USERS?.split(',').map(u => u.trim()).filter(u => u) || [];

  if (adminList.length === 0) {
    console.warn('[ADMIN-CHECK] No admin users configured in ADMIN_USERS environment variable');
    throw new Error('管理员功能未配置');
  }

  if (!adminList.includes(username)) {
    console.warn(`[ADMIN-CHECK] User ${username} attempted to access admin function`);
    throw new Error('需要管理员权限');
  }

  console.log(`[ADMIN-CHECK] Admin access granted for user: ${username}`);
}

// 【新增】批量获取所有用户的用量数据
async function getAllUsersUsage(env, limit = 100, cursor = null) {
  try {
    console.log(`[ADMIN-USAGE] Starting to fetch users usage data. Limit: ${limit}, Cursor: ${cursor || 'null'}`);

    // 获取用户列表
    const listOptions = { prefix: 'user:', limit };
    if (cursor) {
      listOptions.cursor = cursor;
    }

    const userKeys = await env.USERS.list(listOptions);
    console.log(`[ADMIN-USAGE] Found ${userKeys.keys.length} user keys`);

    // 并行获取用户数据
    const promises = userKeys.keys.map(async (key) => {
      try {
        const userDataString = await env.USERS.get(key.name);
        if (!userDataString) {
          console.warn(`[ADMIN-USAGE] No data found for key: ${key.name}`);
          return null;
        }

        const userData = JSON.parse(userDataString);
        const username = key.name.replace('user:', '');

        // 确保usage数据存在（向后兼容）
        const usage = userData.usage || {
          totalChars: 0,
          monthlyChars: 0,
          monthlyResetAt: getNextMonthResetTimestamp()
        };

        // 检查月度重置（但不写回，避免大量写操作）
        const now = Date.now();
        if (now >= usage.monthlyResetAt) {
          usage.monthlyChars = 0;
          usage.monthlyResetAt = getNextMonthResetTimestamp();
        }

        return {
          username,
          usage,
          // 可选：添加一些基本用户信息
          createdAt: userData.createdAt,
          vip: userData.vip || { expireAt: 0, type: null }
        };
      } catch (error) {
        console.error(`[ADMIN-USAGE] Error processing user ${key.name}:`, error);
        return null;
      }
    });

    const results = await Promise.all(promises);
    const usageData = results.filter(item => item !== null);

    console.log(`[ADMIN-USAGE] Successfully processed ${usageData.length} users`);

    return {
      users: usageData,
      pagination: {
        limit,
        hasMore: !userKeys.list_complete,
        cursor: userKeys.cursor || null,
        total: usageData.length
      },
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('[ADMIN-USAGE] Error fetching users usage:', error);
    throw error;
  }
}

// 添加在配置部分之后
function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };
}

// 处理 OPTIONS 请求
function handleOptions(request) {
  return new Response(null, {
    headers: corsHeaders()
  });
}

// 【新增】统一的认证错误处理函数
// 根据错误消息生成结构化的错误响应，便于前端识别和处理
function createAuthErrorResponse(error) {
  // 根据错误消息判断错误类型
  let errorCode = 'AUTH_ERROR';
  let errorMessage = error.message;

  if (error.message === 'Token expired') {
    errorCode = 'TOKEN_EXPIRED';
  } else if (error.message === 'Invalid token' || error.message === 'Invalid signature') {
    errorCode = 'TOKEN_INVALID';
  } else if (error.message === 'Invalid token type') {
    errorCode = 'TOKEN_TYPE_INVALID';
  }

  return new Response(JSON.stringify({
    error: errorMessage,
    code: errorCode // 新增错误码字段，便于前端统一处理
  }), {
    status: 401,
    headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
  });
}

// 【新增】判断是否为认证相关错误的辅助函数
function isAuthError(error) {
  return error.message === 'Token expired' ||
         error.message === 'Invalid token' ||
         error.message === 'Invalid signature' ||
         error.message === 'Invalid token type';
}

// ========== TTS Task Durable Object ==========
// 【完整功能】TTS任务处理的Durable Object
// 每个TTS任务都会创建一个独立的DO实例，支持WebSocket实时通信
export class TtsTaskDomyaitts {
  constructor(state, env) {
    this.state = state;
    this.env = env;
    this.sessions = []; // 存储连接到此DO的WebSocket会话
    this.taskData = {}; // 在内存中存储当前任务的数据

    // 【新增】初始化日志系统
    this.logger = createLogger(env);
    // 初始化日志上下文，taskId使用DO的ID
    this.logContext = {
      username: 'unknown',
      taskId: this.state.id.toString()
    };

    // 从持久化存储中恢复任务数据，以防DO被唤醒
    // 这对于长时间运行的任务很重要
    this.state.storage.get('taskData').then(data => {
        if (data) {
          this.taskData = data;
          // 如果恢复的数据中有username，更新日志上下文
          if (data.username) {
            this.logContext.username = data.username;
          }
        }
    });
  }

  // 【新增】alarm 方法，用于DO的自我清理
  async alarm() {
    console.log(`[DO-ALARM] Triggered for task: ${this.state.id.toString()}.`);

    // 1. 防御性地关闭所有可能残留的 WebSocket 连接
    this.sessions.forEach(session => {
        try {
            // 使用标准关闭码 1000 (Normal Closure)
            session.close(1000, "Task object is being garbage collected.");
        } catch (e) {
            // 忽略错误，因为会话可能已经被客户端关闭
        }
    });
    console.log(`[DO-ALARM] Closed any lingering WebSocket sessions.`);

    // 2. 删除此DO实例的所有持久化状态
    await this.state.storage.deleteAll();
    console.log(`[DO-ALARM] State for task ${this.state.id.toString()} has been deleted.`);
  }

  /**
   * 【新增】获取当前DO实例的数据中心位置
   * @returns {string|null} 当前数据中心代码
   */
  getCurrentLocation() {
    // 尝试从任务数据中获取位置信息
    if (this.taskData && this.taskData.currentLocation) {
      return this.taskData.currentLocation;
    }

    // 如果没有存储位置信息，返回null
    // 在实际部署中，可以通过CF-RAY header或其他方式获取
    return null;
  }

  async fetch(request) {
    // 【新增】记录DO实际运行位置到Analytics Engine
    const taskId = this.state.id.toString();
    const actualColo = request.cf?.colo || "unknown";

    try {
      // 检查是否已经记录过，避免重复记录
      const alreadyRecorded = await this.state.storage.get('analytics_recorded');
      if (!alreadyRecorded && this.env.DO_ANALYTICS) {
        this.env.DO_ANALYTICS.writeDataPoint({
          blobs: [
            taskId,                                    // blob_0: taskId
            "do_instantiated",                         // blob_1: eventType (区分事件)
            this.taskData?.taskType || "unknown",      // blob_2: taskType (从taskData获取)
            "N/A",                                     // blob_3: locationHint (这里不需要)
            actualColo,                                // blob_4: actualColo (这里有值)
            request.cf?.country || "unknown"           // blob_5: country
          ],
          doubles: [1],
          indexes: [taskId]  // 【修复】只使用一个索引，符合免费计划限制
        });

        // 设置标志位，防止重复记录
        await this.state.storage.put('analytics_recorded', true);

        if (this.env.DEBUG) {
          console.log(`[ANALYTICS] Recorded actual location for task ${taskId}, colo: ${actualColo}`);
        }
      }
    } catch (analyticsError) {
      if (this.env.DEBUG) {
        console.error('[ANALYTICS] Failed to record actual location:', analyticsError);
      }
      // 不影响主流程，继续执行
    }

    const upgradeHeader = request.headers.get('Upgrade');
    if (upgradeHeader !== 'websocket') {
      return new Response('Expected Upgrade: websocket', { status: 426 });
    }

    const [client, server] = Object.values(new WebSocketPair());

    // 将 server-side WebSocket 交给我们自己处理
    server.accept();
    this.handleSession(server);

    // 将 client-side WebSocket 返回给运行时
    return new Response(null, {
      status: 101,
      webSocket: client,
    });
  }

  async handleSession(webSocket) {
    this.sessions.push(webSocket);

    // ==================【新增代码】==================
    // 在连接建立后，立即向客户端发送一条包含 taskId 的初始化成功消息。
    // this.state.id.toString() 正是我们需要的 taskId。
    try {
      const initialMessage = {
        type: 'initialized',
        message: 'Connection successful. Task is ready to be started.',
        taskId: this.state.id.toString()
      };
      webSocket.send(JSON.stringify(initialMessage));

      this.logger.info('WebSocket session initialized, sent taskId to client', {
        taskId: this.state.id.toString()
      }, this.logContext);

    } catch (e) {
      this.logger.error(e, this.logContext, {
        action: 'send_initial_message'
      });
      // 如果发送失败，最好关闭连接
      webSocket.close(1011, "Failed to send initial task ID.");
      return;
    }
    // ===============================================

    // 【新增】启动心跳机制
    this.startHeartbeat();

    webSocket.addEventListener('message', async (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.action === 'start') {
          // 【改进】检查任务是否已在运行
          if (this.taskData && this.taskData.status === 'processing') {
            this.logger.warn('Task start rejected - already running', {}, this.logContext);
            webSocket.send(JSON.stringify({ type: 'error', message: 'Task is already running.' }));
            return;
          }

          // 【关键】在验证token后，立即更新日志上下文
          try {
            const username = await verifyToken(data.token, this.env);
            this.logContext.username = username;

            this.logger.info('Task starting', {
              action: 'start',
              taskType: data.taskType || 'single',
              textLength: data.input?.length || 0
            }, this.logContext);
          } catch (tokenError) {
            this.logger.error(tokenError, this.logContext, { action: 'token_verification' });
            webSocket.send(JSON.stringify({ type: 'error', message: '登录过期，请重新登录' }));
            return;
          }

          // 将任务数据存储在DO的内存中
          this.taskData = {
            ...data,
            status: 'processing', // 添加一个内部状态
            taskId: this.state.id.toString(), // DO的ID就是我们的任务ID
            model: data.model || "eleven_turbo_v2", // 默认模型
            username: this.logContext.username // 存储用户名
          };
          // 持久化存储，以备不时之需
          await this.state.storage.put('taskData', this.taskData);

          // 【新增】根据 taskType 分发任务
          if (data.taskType === 'dialogue') {
            await this.runDialogueTtsProcess();
          } else {
            // 默认处理单人TTS任务
            await this.runSingleTtsProcess();
          }
        } else if (data.action === 'retry') {
          // 【新增】处理重试请求，支持任务状态恢复
          if (this.taskData && this.taskData.status === 'processing') {
            this.logger.warn('Task retry rejected - already running', {}, this.logContext);
            webSocket.send(JSON.stringify({ type: 'error', message: 'Task is already running.' }));
            return;
          }

          // 【关键】验证token并更新日志上下文
          try {
            const username = await verifyToken(data.token, this.env);
            this.logContext.username = username;
          } catch (tokenError) {
            this.logger.error(tokenError, this.logContext, { action: 'retry_token_verification' });
            webSocket.send(JSON.stringify({ type: 'error', message: '登录过期，请重新登录' }));
            return;
          }

          // 从重试数据中恢复任务状态
          if (data.recoveryData) {
            this.taskData = {
              ...data.recoveryData,
              status: 'processing',
              taskId: this.state.id.toString(),
              isRetry: true,
              retryCount: (data.recoveryData.retryCount || 0) + 1,
              username: this.logContext.username
            };
          } else {
            // 如果没有恢复数据，使用当前数据
            this.taskData = {
              ...data,
              status: 'processing',
              taskId: this.state.id.toString(),
              model: data.model || "eleven_turbo_v2",
              isRetry: true,
              retryCount: 1,
              username: this.logContext.username
            };
          }

          // 持久化存储
          await this.state.storage.put('taskData', this.taskData);

          this.logger.info('Task retry starting', {
            retryCount: this.taskData.retryCount,
            taskType: this.taskData.taskType || 'single'
          }, this.logContext);

          // 根据任务类型分发
          if (this.taskData.taskType === 'dialogue') {
            await this.runDialogueTtsProcess();
          } else {
            await this.runSingleTtsProcess();
          }
        }
      } catch (error) {
        this.logger.error(error, this.logContext, { action: 'websocket_message_processing' });
        this.broadcast({ type: 'error', message: 'Invalid message format' });
      }
    });

    webSocket.addEventListener('close', () => {
      this.sessions = this.sessions.filter(ws => ws !== webSocket);
      this.logger.debug('WebSocket session closed', { remainingSessions: this.sessions.length }, this.logContext);

      // 【新增】停止心跳机制
      this.stopHeartbeat();
    });

    webSocket.addEventListener('error', (err) => {
        this.logger.error(err, this.logContext, { action: 'websocket_error' });
    });
  }

  // 【新增】心跳和进度更新机制
  startHeartbeat() {
    const timeoutConfig = getSmartTimeoutConfig(this.env);

    // 启动心跳定时器
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, timeoutConfig.HEARTBEAT_INTERVAL);

    // 启动进度更新定时器
    this.progressInterval = setInterval(() => {
      this.sendProgressUpdate();
    }, timeoutConfig.PROGRESS_UPDATE_INTERVAL);

    this.logger.debug('Heartbeat mechanism started', {
      heartbeatInterval: timeoutConfig.HEARTBEAT_INTERVAL,
      progressInterval: timeoutConfig.PROGRESS_UPDATE_INTERVAL
    }, this.logContext);
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }
    this.logger.debug('Heartbeat mechanism stopped', {}, this.logContext);
  }

  sendHeartbeat() {
    try {
      const heartbeatMessage = {
        type: 'heartbeat',
        timestamp: Date.now(),
        taskId: this.state.id.toString()
      };
      this.broadcast(heartbeatMessage);
    } catch (error) {
      this.logger.error(error, this.logContext, { action: 'send_heartbeat' });
    }
  }

  sendProgressUpdate() {
    try {
      // 只在任务处理期间发送进度更新
      if (this.taskData && this.taskData.status === 'processing') {
        const progressMessage = {
          type: 'progress_update',
          message: this.getProgressMessage(),
          timestamp: Date.now(),
          taskId: this.state.id.toString()
        };
        this.broadcast(progressMessage);
      }
    } catch (error) {
      this.logger.error(error, this.logContext, { action: 'send_progress_update' });
    }
  }

  getProgressMessage() {
    if (!this.taskData) return '正在处理...';

    const currentStep = this.taskData.currentStep || 'processing';
    const stepMessages = {
      'processing': '正在处理任务...',
      'text_processing': '正在分析文本...',
      'voice_mapping': '正在获取语音配置...',
      'audio_generation': '正在生成音频...',
      'proxy_failover': '正在尝试备用服务器...',
      'audio_merging': '正在合并音频文件...',
      'r2_storage': '正在保存音频文件...',
      'finalizing': '正在完成处理...'
    };

    return stepMessages[currentStep] || '正在处理...';
  }

  // 【新增】更新任务步骤
  updateTaskStep(step) {
    if (this.taskData) {
      this.taskData.currentStep = step;
      this.logger.debug('Task step updated', { step }, this.logContext);
    }
  }

  // 【核心修改】将 processAudioAsync 的逻辑移入此处，并增加 alarm 清理机制
  // 【名称变更】runTtsProcess -> runSingleTtsProcess
  async runSingleTtsProcess() {
    // 【新增】获取任务级重试配置
    const retryConfig = getTaskRetryConfig(this.env);

    // 【优化】定义不同的清理延迟时间
    const SUCCESS_CLEANUP_DELAY_MS = 1 * 24 * 60 * 60 * 1000; // 成功任务状态保留1天
    const FAILURE_CLEANUP_DELAY_MS = 1 * 60 * 60 * 1000;     // 失败任务状态保留1小时
    let cleanupDelay = FAILURE_CLEANUP_DELAY_MS; // 默认为失败时的清理延迟

    // 从 this.taskData 获取所需参数
    const { taskId, input, voice, stability, similarity_boost, style, speed, model, token } = this.taskData;

    // 【修复】智能处理 voice 参数
    // 优先尝试通过名称在映射中查找ID（兼容旧版客户端）
    const voiceId = await getVoiceId(voice, this.env);

    // 【修复】按照问题分析文档的建议，使用外层try-catch-finally结构
    try {
      // 【新增】任务级重试逻辑 - 方案B：基于策略切换的渐进式重试
      if (retryConfig.ENABLE_TASK_RETRY) {
        const maxAttempts = retryConfig.MAX_TASK_RETRIES + 1; // 总尝试次数
        let lastError = null;
        let failedLocations = []; // 记录失败的数据中心位置
        const taskStartTime = Date.now();

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
          try {
            // 获取当前尝试的策略
            const strategy = getRetryStrategy(attempt, failedLocations);

            if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
              this.logger.info(`Starting TTS task attempt ${attempt}/${maxAttempts}`, {
                strategy: strategy.name,
                description: strategy.description,
                proxyMode: strategy.proxyMode,
                excludeLocations: strategy.excludeLocations,
                elapsedTime: Date.now() - taskStartTime
              }, this.logContext);
            }

            // 如果是重试，向前端发送友好的进度提示
            if (attempt > 1) {
              this.broadcastProgress(strategy.userMessage);

              // 计算并等待重试延迟
              const retryDelay = calculateRetryDelay(attempt, retryConfig);
              if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
                this.logger.info(`Waiting ${retryDelay}ms before retry attempt`, {
                  attempt,
                  retryDelay,
                  strategy: strategy.name
                }, this.logContext);
              }

              await new Promise(resolve => setTimeout(resolve, retryDelay));
            }

            // 应用重试策略到环境配置
            const strategyEnv = applyRetryStrategy(this.env, strategy);

            // 执行核心TTS处理逻辑
            const result = await this.executeSingleTtsCore(taskId, input, voiceId, model, stability, similarity_boost, style, speed, token, strategyEnv);

            // 成功完成，设置成功清理延迟
            cleanupDelay = SUCCESS_CLEANUP_DELAY_MS;

            if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
              this.logger.info(`TTS task completed successfully on attempt ${attempt}`, {
                totalAttempts: attempt,
                totalElapsedTime: Date.now() - taskStartTime,
                strategy: strategy.name
              }, this.logContext);
            }

            // 成功时跳出重试循环
            break;

          } catch (error) {
            lastError = error;

            // 【新增】如果检测到内容违规，立即终止所有重试
            if (error.isContentViolation) {
              if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
                this.logger.warn('Content violation detected. Immediately terminating task retries.', {
                  attempt: attempt,
                  error: error.message,
                  isContentViolation: true
                }, this.logContext);
              }
              break; // 中断 for 循环，直接跳到最终错误处理
            }

            // 【新增修复】优先检查是否为不可重试的用户级错误
            if (error.cause === 'quota') {
              if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
                this.logger.warn('User-level quota/VIP error detected. Immediately terminating task retries.', {
                  attempt: attempt,
                  error: error.message
                }, this.logContext);
              }
              break; // 立即中断重试循环
            }

            // 记录失败的数据中心位置（如果有的话）
            const currentLocation = this.getCurrentLocation();
            if (currentLocation && !failedLocations.includes(currentLocation)) {
              failedLocations.push(currentLocation);
            }

            if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
              this.logger.warn(`TTS task attempt ${attempt} failed`, {
                attempt,
                maxAttempts,
                error: error.message,
                isDataCenterRetryable: error.isDataCenterRetryable,
                currentLocation,
                failedLocations,
                willRetry: attempt < maxAttempts,
                elapsedTime: Date.now() - taskStartTime
              }, this.logContext);
            }

            // 如果是最后一次尝试，跳出循环
            if (attempt >= maxAttempts) {
              break;
            }

            // 检查绝对超时
            if (Date.now() - taskStartTime >= retryConfig.TASK_ABSOLUTE_TIMEOUT) {
              this.logger.warn('Task retry stopped due to absolute timeout', {
                elapsedTime: Date.now() - taskStartTime,
                absoluteTimeout: retryConfig.TASK_ABSOLUTE_TIMEOUT
              }, this.logContext);
              break;
            }
          }
        }

        // 所有重试都失败了，处理最终错误
        if (lastError) {
          await this.handleFinalTaskFailure(lastError, taskId, failedLocations);
        }
      } else {
        // 重试功能被禁用，使用原有的单次执行逻辑
        try {
          const result = await this.executeSingleTtsCore(taskId, input, voiceId, model, stability, similarity_boost, style, speed, token, this.env);
          cleanupDelay = SUCCESS_CLEANUP_DELAY_MS;
        } catch (error) {
          await this.handleFinalTaskFailure(error, taskId, []);
        }
      }

    } catch (outerError) {
      // 捕获顶层的、意料之外的错误
      this.logger.error(outerError, this.logContext, {
        message: 'Unexpected error in runSingleTtsProcess'
      });
      await this.handleFinalTaskFailure(outerError, taskId, []);
    } finally {
      // 【修复】确保清理逻辑总是在最后执行
      try {
          // 1. 关闭所有 WebSocket 连接，通知客户端任务已结束
          this.logger.info('Closing all WebSocket sessions', {
            sessionCount: this.sessions.length
          }, this.logContext);

          this.sessions.forEach(s => {
              try {
                  s.close(1000, "Task finished.");
              } catch (e) { /* 忽略错误 */ }
          });

          // 2. 计算最终的清理时间
          const cleanupTime = Date.now() + cleanupDelay;

          // 3. 使用 blockConcurrencyWhile 安全地设置闹钟，防止并发问题
          this.logger.info('Task finished, scheduling cleanup', {
            cleanupTime: new Date(cleanupTime).toISOString(),
            cleanupDelayMs: cleanupDelay
          }, this.logContext);

          try {
              await this.state.blockConcurrencyWhile(async () => {
                  await this.state.storage.setAlarm(cleanupTime);
              });
              this.logger.debug('Cleanup alarm set successfully', {}, this.logContext);
          } catch (alarmError) {
              // 记录设置闹钟失败的错误，这是需要关注的重要运维问题
              this.logger.error(alarmError, this.logContext, {
                message: 'CRITICAL: Failed to set cleanup alarm',
                cleanupTime: new Date(cleanupTime).toISOString()
              });
          }
      } catch (cleanupError) {
          // 记录清理过程中的错误，但不影响主流程
          this.logger.error(cleanupError, this.logContext, {
            message: 'Error during cleanup process'
          });
      }
    }
  }

  /**
   * 【新增】执行单人TTS任务的核心逻辑
   * 从原有的runSingleTtsProcess中提取出来，供重试机制调用
   * @param {string} taskId - 任务ID
   * @param {string} input - 输入文本
   * @param {string} voiceId - 语音ID
   * @param {string} model - 模型名称
   * @param {number} stability - 稳定性参数
   * @param {number} similarity_boost - 相似度增强参数
   * @param {number} style - 风格参数
   * @param {number} speed - 语速参数
   * @param {string} token - 认证令牌
   * @param {object} env - 环境对象（可能已应用重试策略）
   */
  async executeSingleTtsCore(taskId, input, voiceId, model, stability, similarity_boost, style, speed, token, env) {
    // 【安全增强】在DO内部直接从token获取可信的用户名
    const username = await verifyToken(token, env);

    // 【日志优化】将username存储到taskData，便于后续日志追踪
    this.taskData.username = username;
    // 同时更新日志上下文
    this.logContext.username = username;

    this.logger.info('Single TTS task core execution starting', {
      textLength: input.length,
      voice: voiceId,
      model: model
    }, this.logContext);

    // --- START: 调整顺序 ---
    // 1. 先计算字符数
    this.updateTaskStep('text_processing');
    const charCount = input.length;

    // 2. 再调用增强后的 checkVip 进行检查（包含配额检查）
    await checkVip(username, env, 'STANDARD', charCount);
    // --- END: 调整顺序 ---

    // 【关键】在env中注入日志上下文，供下游函数使用
    const enhancedEnv = enhanceEnvWithLogging(env, this.logContext);

    // 【改进】使用智能进度消息广播，可通过环境变量控制
    this.broadcastProgress('任务初始化...');

    // 步骤2: 文本分割
    this.updateTaskStep('text_processing');
    const chunks = await splitText(input);
    this.broadcastProgress(`文本已分割为 ${chunks.length} 个片段`);

    // 步骤3: TTS音频生成
    this.updateTaskStep('audio_generation');
    this.broadcastProgress(`正在生成 ${chunks.length} 个音频片段...`);
    // 【修改点】创建包含 taskId 和 username 的上下文
    const processContext = {
      taskId: this.state.id.toString(),
      username: this.logContext.username,
      updateTaskStep: (step) => this.updateTaskStep(step)
    };

    const audioDataList = await processChunks(chunks, voiceId, model, stability, similarity_boost, style, speed, enhancedEnv, processContext);

    // 步骤4: 音频合并
    this.updateTaskStep('audio_merging');
    this.broadcastProgress('正在合并音频...');
    const totalLength = audioDataList.reduce((acc, curr) => acc + curr.byteLength, 0);
    const combinedAudioData = new Uint8Array(totalLength);
    let offset = 0;
    for (const audioData of audioDataList) {
      combinedAudioData.set(new Uint8Array(audioData), offset);
      offset += audioData.byteLength;
    }

    // 步骤5: R2存储
    this.updateTaskStep('r2_storage');
    this.broadcastProgress('正在将文件存入云存储...');
    await storeAudioFile(taskId, combinedAudioData.buffer, enhancedEnv);

    // 步骤6: 任务完成
    const r2DirectUrl = R2_DIRECT_DOWNLOAD_CONFIG.generateUrl(taskId);
    const finalStatus = {
        status: 'complete',
        downloadUrl: r2DirectUrl,
        streamUrl: r2DirectUrl, // 【修复】添加streamUrl字段，用于前端音频播放
        audioSize: totalLength,
        username: username,
        completedAt: Date.now(),
        taskId: taskId, // 【新增】返回任务ID，便于前端识别对应的任务
    };

    this.logger.info('Single TTS task core execution completed successfully', {
      audioSize: totalLength,
      downloadUrl: r2DirectUrl
    }, this.logContext);

    // 【新增】更新用户字符数统计
    await this.updateUserUsage(username, charCount);

    // 将最终结果存入KV，用于历史记录查询
    await storeStatusKV(enhancedEnv, taskId, finalStatus);

    // 通过WebSocket发送最终成功结果
    this.broadcast({ type: 'complete', ...finalStatus });

    // 更新内部状态为完成
    this.taskData.status = 'complete';
    await this.state.storage.put('taskData', this.taskData);

    return finalStatus;
  }

  /**
   * 【新增】处理任务最终失败的逻辑
   * @param {Error} error - 最终的错误对象
   * @param {string} taskId - 任务ID
   * @param {string[]} failedLocations - 失败的数据中心位置列表
   */
  async handleFinalTaskFailure(error, taskId, failedLocations) {
    this.logger.error(error, this.logContext, {
      message: 'Single TTS task failed after all retries',
      taskId: taskId,
      failedLocations: failedLocations
    });

    // 【新增】优先检查是否为内容违规错误
    if (error.isContentViolation) {
      this.logger.warn('Task failed due to content violation', {
        errorMessage: error.message,
        isContentViolation: true
      }, this.logContext);

      const violationPayload = {
        type: 'error',
        message: error.message, // 直接使用原始的违规消息
        errorType: 'content_violation',
        isRetryable: false
      };

      this.broadcast(violationPayload);

      // 更新状态为内容违规失败
      this.taskData.status = 'content_violation_failed';
      await this.state.storage.put('taskData', this.taskData);

      await storeStatusKV(this.env, taskId, {
        status: 'content_violation_failed',
        error: error.message,
        errorType: 'content_violation',
        username: this.taskData.username
      });

      return; // 直接返回，不执行其他重试逻辑
    }

    // 【新增】检查是否为代理故障转移失败
    if (error.isProxyFailoverFailure) {
      this.logger.error('Task failed due to proxy failover failure', {
        errorMessage: error.message,
        originalError: error.originalError?.message,
        proxyError: error.proxyError?.message,
        taskId: taskId,
        isProxyFailoverFailure: true
      }, this.logContext);

      // 代理故障转移失败，发送特定的错误消息
      const proxyFailoverPayload = {
        type: 'error',
        message: '主备服务器均不可用，请稍后再试或联系客服',
        errorType: 'proxy_failover_failure',
        isRetryable: false
      };

      this.broadcast(proxyFailoverPayload);

      // 更新状态为代理故障转移失败
      this.taskData.status = 'proxy_failover_failed';
      await this.state.storage.put('taskData', this.taskData);

      await storeStatusKV(this.env, taskId, {
        status: 'proxy_failover_failed',
        error: error.message,
        errorType: 'proxy_failover_failure',
        originalError: error.originalError?.message,
        proxyError: error.proxyError?.message,
        username: this.taskData.username
      });

      return; // 直接返回，不执行其他重试逻辑
    }

    // 【保持原有逻辑】检查是否为数据中心级别的可重试错误
    if (error.isDataCenterRetryable) {
        this.logger.warn('Task failed with retryable error, suggesting datacenter switch', {
          errorMessage: error.message,
          isDataCenterRetryable: true,
          failedLocations: failedLocations
        }, this.logContext);

        // 获取当前DO的位置信息（从环境或状态中）
        const currentLocation = this.getCurrentLocation();
        const excludeLocations = currentLocation ? [currentLocation, ...failedLocations] : failedLocations;

        const retryablePayload = {
            type: 'error_retryable',
            message: '当前数据中心暂时不可用，正在尝试切换到其他区域...',
            error: error.message,
            excludeLocations: excludeLocations,
            taskData: {
                // 保存任务的关键信息供重试使用
                input: this.taskData.input,
                voice: this.taskData.voice,
                model: this.taskData.model,
                stability: this.taskData.stability,
                similarity_boost: this.taskData.similarity_boost,
                style: this.taskData.style,
                speed: this.taskData.speed,
                username: this.taskData.username,
                taskType: this.taskData.taskType
            }
        };

        this.broadcast(retryablePayload);

        // 更新内部状态为可重试失败
        this.taskData.status = 'retryable_failed';
        await this.state.storage.put('taskData', this.taskData);

        // 将可重试失败状态存入KV
        await storeStatusKV(this.env, taskId, {
            status: 'retryable_failed',
            error: error.message,
            username: this.taskData.username,
            excludeLocations: excludeLocations
        });
    } else {
        // 不可重试的错误，按原有逻辑处理
        const errorPayload = {
            type: 'error',
            message: error.message || '任务处理失败'
        };
        // 检查是否是会员错误
        if (error.cause === 'quota') {
            errorPayload.message = '会员权限不足或已过期，请充值。';
        }
        this.broadcast(errorPayload);

        // 更新内部状态为失败
        this.taskData.status = 'failed';
        await this.state.storage.put('taskData', this.taskData);

        // 同样将失败状态存入KV
        await storeStatusKV(this.env, taskId, { status: 'failed', error: error.message, username: this.taskData.username });
    }
  }

  /**
   * 【新增】处理多人对话音频生成的函数
   * 编排整个多人对话的生成过程，并充分利用现有工具函数
   */
  async runDialogueTtsProcess() {
    // 【新增】获取任务级重试配置
    const retryConfig = getTaskRetryConfig(this.env);

    const SUCCESS_CLEANUP_DELAY_MS = 1 * 24 * 60 * 60 * 1000;
    const FAILURE_CLEANUP_DELAY_MS = 1 * 60 * 60 * 1000;
    let cleanupDelay = FAILURE_CLEANUP_DELAY_MS;

    const { taskId, dialogue, model, stability, similarity_boost, style, speed, token } = this.taskData;

    // 【修复】按照问题分析文档的建议，使用外层try-catch-finally结构
    try {
      // 【新增】任务级重试逻辑 - 方案B：基于策略切换的渐进式重试
      if (retryConfig.ENABLE_TASK_RETRY) {
        const maxAttempts = retryConfig.MAX_TASK_RETRIES + 1; // 总尝试次数
        let lastError = null;
        let failedLocations = []; // 记录失败的数据中心位置
        const taskStartTime = Date.now();

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
          try {
            // 获取当前尝试的策略
            const strategy = getRetryStrategy(attempt, failedLocations);

            if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
              this.logger.info(`Starting Dialogue TTS task attempt ${attempt}/${maxAttempts}`, {
                strategy: strategy.name,
                description: strategy.description,
                proxyMode: strategy.proxyMode,
                excludeLocations: strategy.excludeLocations,
                elapsedTime: Date.now() - taskStartTime
              }, this.logContext);
            }

            // 如果是重试，向前端发送友好的进度提示
            if (attempt > 1) {
              this.broadcastProgress(strategy.userMessage);

              // 计算并等待重试延迟
              const retryDelay = calculateRetryDelay(attempt, retryConfig);
              if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
                this.logger.info(`Waiting ${retryDelay}ms before retry attempt`, {
                  attempt,
                  retryDelay,
                  strategy: strategy.name
                }, this.logContext);
              }

              await new Promise(resolve => setTimeout(resolve, retryDelay));
            }

            // 应用重试策略到环境配置
            const strategyEnv = applyRetryStrategy(this.env, strategy);

            // 执行核心对话TTS处理逻辑
            const result = await this.executeDialogueTtsCore(taskId, dialogue, model, stability, similarity_boost, style, speed, token, strategyEnv);

            // 成功完成，设置成功清理延迟
            cleanupDelay = SUCCESS_CLEANUP_DELAY_MS;

            if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
              this.logger.info(`Dialogue TTS task completed successfully on attempt ${attempt}`, {
                totalAttempts: attempt,
                totalElapsedTime: Date.now() - taskStartTime,
                strategy: strategy.name
              }, this.logContext);
            }

            // 成功时跳出重试循环
            break;

          } catch (error) {
            lastError = error;

            // 【新增】如果检测到内容违规，立即终止所有重试
            if (error.isContentViolation) {
              if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
                this.logger.warn('Content violation detected in dialogue task. Immediately terminating task retries.', {
                  attempt: attempt,
                  error: error.message,
                  isContentViolation: true
                }, this.logContext);
              }
              break; // 中断 for 循环，直接跳到最终错误处理
            }

            // 【新增修复】优先检查是否为不可重试的用户级错误
            if (error.cause === 'quota') {
              if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
                this.logger.warn('User-level quota/VIP error detected in dialogue task. Immediately terminating task retries.', {
                  attempt: attempt,
                  error: error.message
                }, this.logContext);
              }
              break; // 立即中断重试循环
            }

            // 记录失败的数据中心位置（如果有的话）
            const currentLocation = this.getCurrentLocation();
            if (currentLocation && !failedLocations.includes(currentLocation)) {
              failedLocations.push(currentLocation);
            }

            if (retryConfig.ENABLE_TASK_RETRY_DEBUG) {
              this.logger.warn(`Dialogue TTS task attempt ${attempt} failed`, {
                attempt,
                maxAttempts,
                error: error.message,
                isDataCenterRetryable: error.isDataCenterRetryable,
                currentLocation,
                failedLocations,
                willRetry: attempt < maxAttempts,
                elapsedTime: Date.now() - taskStartTime
              }, this.logContext);
            }

            // 如果是最后一次尝试，跳出循环
            if (attempt >= maxAttempts) {
              break;
            }

            // 检查绝对超时
            if (Date.now() - taskStartTime >= retryConfig.TASK_ABSOLUTE_TIMEOUT) {
              this.logger.warn('Dialogue task retry stopped due to absolute timeout', {
                elapsedTime: Date.now() - taskStartTime,
                absoluteTimeout: retryConfig.TASK_ABSOLUTE_TIMEOUT
              }, this.logContext);
              break;
            }
          }
        }

        // 所有重试都失败了，处理最终错误
        if (lastError) {
          await this.handleFinalDialogueFailure(lastError, taskId, failedLocations);
        }
      } else {
        // 重试功能被禁用，使用原有的单次执行逻辑
        try {
          const result = await this.executeDialogueTtsCore(taskId, dialogue, model, stability, similarity_boost, style, speed, token, this.env);
          cleanupDelay = SUCCESS_CLEANUP_DELAY_MS;
        } catch (error) {
          await this.handleFinalDialogueFailure(error, taskId, []);
        }
      }

    } catch (outerError) {
      // 捕获顶层的、意料之外的错误
      this.logger.error(outerError, this.logContext, {
        message: 'Unexpected error in runDialogueTtsProcess'
      });
      await this.handleFinalDialogueFailure(outerError, taskId, []);
    } finally {
      // 【修复】确保清理逻辑总是在最后执行
      try {
          // 1. 关闭所有 WebSocket 连接，通知客户端任务已结束
          this.logger.info('Closing all WebSocket sessions', {
            sessionCount: this.sessions.length
          }, this.logContext);

          this.sessions.forEach(s => {
              try {
                  s.close(1000, "Task finished.");
              } catch (e) { /* 忽略错误 */ }
          });

          // 2. 计算最终的清理时间
          const cleanupTime = Date.now() + cleanupDelay;

          // 3. 使用 blockConcurrencyWhile 安全地设置闹钟，防止并发问题
          this.logger.info('Dialogue task finished, scheduling cleanup', {
            cleanupTime: new Date(cleanupTime).toISOString(),
            cleanupDelayMs: cleanupDelay
          }, this.logContext);

          try {
              await this.state.blockConcurrencyWhile(async () => {
                  await this.state.storage.setAlarm(cleanupTime);
              });
              this.logger.debug('Cleanup alarm set successfully', {}, this.logContext);
          } catch (alarmError) {
              // 记录设置闹钟失败的错误，这是需要关注的重要运维问题
              this.logger.error(alarmError, this.logContext, {
                message: 'CRITICAL: Failed to set cleanup alarm',
                cleanupTime: new Date(cleanupTime).toISOString()
              });
          }
      } catch (cleanupError) {
          // 记录清理过程中的错误，但不影响主流程
          this.logger.error(cleanupError, this.logContext, {
            message: 'Error during cleanup process'
          });
      }
    }
  }

  /**
   * 【声音分组并发优化】执行多人对话TTS任务的核心逻辑
   * 采用声音分组并发处理策略，显著提升性能
   * @param {string} taskId - 任务ID
   * @param {Array} dialogue - 对话数组
   * @param {string} model - 模型名称
   * @param {number} stability - 稳定性参数
   * @param {number} similarity_boost - 相似度增强参数
   * @param {number} style - 风格参数
   * @param {number} speed - 语速参数
   * @param {string} token - 认证令牌
   * @param {object} env - 环境对象（可能已应用重试策略）
   */
  async executeDialogueTtsCore(taskId, dialogue, model, stability, similarity_boost, style, speed, token, env) {
    const username = await verifyToken(token, env);

    // 【日志优化】将username存储到taskData，便于后续日志追踪
    this.taskData.username = username;
    // 同时更新日志上下文
    this.logContext.username = username;

    this.logger.info('Dialogue TTS task core execution starting', {
      speakerCount: dialogue?.length || 0,
      model: model
    }, this.logContext);

    // --- START: 调整顺序 ---
    // 1. 先计算总字符数
    const charCount = dialogue.reduce((sum, speaker) => sum + (speaker.text ? speaker.text.length : 0), 0);

    // 2. 再调用增强后的 checkVip 进行检查 (PRO权限 + 配额检查)
    await checkVip(username, env, 'PRO', charCount);
    // --- END: 调整顺序 ---

    // 【关键】在env中注入日志上下文，供下游函数使用
    const enhancedEnv = enhanceEnvWithLogging(env, this.logContext);

    this.broadcastProgress('初始化多人对话任务...');

    if (!Array.isArray(dialogue) || dialogue.length === 0) {
      throw new Error('请求中的 "dialogue" 必须是一个非空数组。');
    }

    // ========== 【声音分组并发优化】核心实施 ==========

    // 第一步：声音分组，保持原始位置信息
    this.broadcastProgress('分析对话结构...');
    const voiceGroups = new Map();
    dialogue.forEach((speaker, index) => {
      if (!voiceGroups.has(speaker.voice)) {
        voiceGroups.set(speaker.voice, []);
      }
      voiceGroups.get(speaker.voice).push({
        ...speaker,
        originalIndex: index
      });
    });

    const uniqueVoices = Array.from(voiceGroups.keys());
    this.logger.info('Voice grouping completed', {
      totalSpeakers: dialogue.length,
      uniqueVoices: uniqueVoices.length,
      voiceDistribution: Object.fromEntries(
        Array.from(voiceGroups.entries()).map(([voice, speakers]) => [voice, speakers.length])
      )
    }, this.logContext);

    this.broadcastProgress(`检测到 ${uniqueVoices.length} 种不同声音，开始并发处理...`);

    // 第二步：不同声音并发处理
    const voiceProcessingPromises = Array.from(voiceGroups.entries()).map(
      async ([voice, speakers], voiceGroupIndex) => {
        const voiceStartTime = Date.now();

        try {
          // 每个声音只查询一次语音ID
          const voiceId = await getVoiceId(voice, env);

          this.logger.flow('voice-group-processing', 'started', this.logContext, {
            voice: voice,
            voiceId: voiceId,
            speakerCount: speakers.length,
            speakerPositions: speakers.map(s => s.originalIndex),
            voiceGroupIndex: voiceGroupIndex + 1,
            totalVoiceGroups: uniqueVoices.length
          });

          const audioResults = [];

          // 同一声音内部仍按顺序处理（保持对话逻辑）
          for (let speakerIndex = 0; speakerIndex < speakers.length; speakerIndex++) {
            const speaker = speakers[speakerIndex];
            const speakerStartTime = Date.now();
            const speakerName = speaker.name || `说话者${speaker.originalIndex + 1}`;

            // 【优化进度反馈】显示声音组内的处理进度
            const voiceGroupProgress = `${voice}组 ${speakerIndex + 1}/${speakers.length}`;
            this.broadcastProgress(`正在处理 ${voiceGroupProgress} (${speakerName})...`);

            this.logger.flow('speaker-in-voice-group', 'started', this.logContext, {
              voice: voice,
              originalIndex: speaker.originalIndex,
              speakerName: speakerName,
              textLength: speaker.text?.length || 0,
              voiceGroupProgress: voiceGroupProgress
            });

            try {
              const chunks = await splitText(speaker.text);

              // 【修改点】创建包含声音组和说话者信息的上下文
              const processContext = {
                taskId: this.state.id.toString(),
                username: this.logContext.username,
                speakerIndex: `${speaker.originalIndex + 1}/${dialogue.length}`,
                speakerName: speakerName,
                voiceGroup: voice,
                voiceGroupProgress: voiceGroupProgress,
                updateTaskStep: (step) => this.updateTaskStep(step)
              };

              const speakerAudioList = await processChunks(
                chunks, voiceId, model, stability, similarity_boost, style, speed, enhancedEnv, processContext
              );

              if (speakerAudioList.length === 0) {
                throw new Error(`未能为说话者 ${speakerName} (${voice}) 生成任何音频。`);
              }

              const combinedAudio = combineAudio(speakerAudioList);

              audioResults.push({
                originalIndex: speaker.originalIndex,
                audio: combinedAudio
              });

              const speakerDuration = Date.now() - speakerStartTime;

              this.logger.flow('speaker-in-voice-group', 'completed', this.logContext, {
                voice: voice,
                originalIndex: speaker.originalIndex,
                speakerName: speakerName,
                chunkCount: chunks.length,
                audioSize: combinedAudio.byteLength,
                duration: speakerDuration,
                voiceGroupProgress: voiceGroupProgress
              });

            } catch (speakerError) {
              const speakerDuration = Date.now() - speakerStartTime;

              this.logger.error(speakerError, this.logContext, {
                operation: 'speaker-in-voice-group-failed',
                voice: voice,
                originalIndex: speaker.originalIndex,
                speakerName: speakerName,
                duration: speakerDuration,
                textLength: speaker.text?.length || 0,
                voiceGroupProgress: voiceGroupProgress
              });

              // 重新抛出错误，让声音组级别处理
              throw speakerError;
            }
          }

          const voiceDuration = Date.now() - voiceStartTime;

          this.logger.flow('voice-group-processing', 'completed', this.logContext, {
            voice: voice,
            speakerCount: speakers.length,
            totalAudioSize: audioResults.reduce((sum, result) => sum + result.audio.byteLength, 0),
            duration: voiceDuration,
            voiceGroupIndex: voiceGroupIndex + 1,
            totalVoiceGroups: uniqueVoices.length
          });

          return audioResults;

        } catch (voiceError) {
          const voiceDuration = Date.now() - voiceStartTime;

          this.logger.error(voiceError, this.logContext, {
            operation: 'voice-group-processing-failed',
            voice: voice,
            speakerCount: speakers.length,
            duration: voiceDuration,
            voiceGroupIndex: voiceGroupIndex + 1,
            totalVoiceGroups: uniqueVoices.length
          });

          // 重新抛出错误，让上层处理
          throw voiceError;
        }
      }
    );

    // 等待所有声音组完成
    this.broadcastProgress('音频生成完成，正在组装...');
    const allVoiceResults = await Promise.all(voiceProcessingPromises);

    // 第三步：音频重组，按原始顺序重新组装
    const finalAudioArray = new Array(dialogue.length);
    allVoiceResults.flat().forEach(result => {
      finalAudioArray[result.originalIndex] = result.audio;
    });

    // 验证音频完整性
    for (let i = 0; i < finalAudioArray.length; i++) {
      if (!finalAudioArray[i]) {
        throw new Error(`位置 ${i} 的音频丢失，请重试。`);
      }
    }

    this.logger.info('Audio reordering completed', {
      totalSpeakers: dialogue.length,
      audioArrayLength: finalAudioArray.length,
      allPositionsFilled: finalAudioArray.every(audio => audio !== undefined)
    }, this.logContext);

    // ========== 【声音分组并发优化】实施完成 ==========

    this.broadcastProgress('正在合并所有对话音频...');
    const finalAudio = combineAudio(finalAudioArray);

    this.broadcastProgress('正在将文件存入云存储...');
    await storeAudioFile(taskId, finalAudio, enhancedEnv);

    const r2DirectUrl = R2_DIRECT_DOWNLOAD_CONFIG.generateUrl(taskId);
    const finalStatus = {
      status: 'complete',
      downloadUrl: r2DirectUrl,
      streamUrl: r2DirectUrl, // 【修复】添加streamUrl字段，用于前端音频播放
      audioSize: finalAudio.byteLength,
      username: username,
      completedAt: Date.now(),
      taskId: taskId, // 【新增】返回任务ID，便于前端识别对应的任务
    };

    this.logger.info('Dialogue TTS task core execution completed successfully', {
      audioSize: finalAudio.byteLength,
      downloadUrl: r2DirectUrl,
      speakerCount: dialogue.length,
      uniqueVoices: uniqueVoices.length,
      optimizationApplied: 'voice_grouping_concurrency'
    }, this.logContext);

    // 【新增】更新用户字符数统计
    await this.updateUserUsage(username, charCount);

    await storeStatusKV(enhancedEnv, taskId, finalStatus);
    this.broadcast({ type: 'complete', ...finalStatus });

    this.taskData.status = 'complete';
    await this.state.storage.put('taskData', this.taskData);

    return finalStatus;
  }

  /**
   * 【新增】处理对话任务最终失败的逻辑
   * @param {Error} error - 最终的错误对象
   * @param {string} taskId - 任务ID
   * @param {string[]} failedLocations - 失败的数据中心位置列表
   */
  async handleFinalDialogueFailure(error, taskId, failedLocations) {
    this.logger.error(error, this.logContext, {
      message: 'Dialogue TTS task failed after all retries',
      taskId: taskId,
      failedLocations: failedLocations
    });

    // 【新增】优先检查是否为内容违规错误
    if (error.isContentViolation) {
      this.logger.warn('Dialogue task failed due to content violation', {
        errorMessage: error.message,
        isContentViolation: true
      }, this.logContext);

      const violationPayload = {
        type: 'error',
        message: error.message, // 直接使用原始的违规消息
        errorType: 'content_violation',
        isRetryable: false
      };

      this.broadcast(violationPayload);

      // 更新状态为内容违规失败
      this.taskData.status = 'content_violation_failed';
      await this.state.storage.put('taskData', this.taskData);

      await storeStatusKV(this.env, taskId, {
        status: 'content_violation_failed',
        error: error.message,
        errorType: 'content_violation',
        username: this.taskData.username
      });

      return; // 直接返回，不执行其他错误处理逻辑
    }

    // 【新增】检查是否为代理故障转移失败
    if (error.isProxyFailoverFailure) {
      this.logger.error('Dialogue task failed due to proxy failover failure', {
        errorMessage: error.message,
        originalError: error.originalError?.message,
        proxyError: error.proxyError?.message,
        taskId: taskId,
        isProxyFailoverFailure: true
      }, this.logContext);

      // 代理故障转移失败，发送特定的错误消息
      const proxyFailoverPayload = {
        type: 'error',
        message: '主备服务器均不可用，请稍后再试或联系客服',
        errorType: 'proxy_failover_failure',
        isRetryable: false
      };

      this.broadcast(proxyFailoverPayload);

      // 更新状态为代理故障转移失败
      this.taskData.status = 'proxy_failover_failed';
      await this.state.storage.put('taskData', this.taskData);

      await storeStatusKV(this.env, taskId, {
        status: 'proxy_failover_failed',
        error: error.message,
        errorType: 'proxy_failover_failure',
        originalError: error.originalError?.message,
        proxyError: error.proxyError?.message,
        username: this.taskData.username
      });

      return; // 直接返回，不执行其他错误处理逻辑
    }

    const errorPayload = {
      type: 'error',
      message: error.message || '多人对话任务处理失败'
    };

    if (error.cause === 'quota') {
      // 根据错误信息判断是权限等级不足还是会员过期
      if (error.message.includes('PRO会员权限')) {
        errorPayload.message = '多人对话功能需要PRO会员权限，请升级到PRO套餐后使用。';
      } else {
        errorPayload.message = '会员权限不足或已过期，请充值。';
      }
    }

    this.broadcast(errorPayload);

    this.taskData.status = 'failed';
    await this.state.storage.put('taskData', this.taskData);
    await storeStatusKV(this.env, taskId, { status: 'failed', error: error.message, username: this.taskData.username });
  }

  broadcast(message) {
    const serializedMessage = JSON.stringify(message);
    this.sessions.forEach(session => {
      try {
        session.send(serializedMessage);
      } catch (error) {
        // 如果发送失败，可能是会话已关闭，可以安全地忽略或记录日志
        console.error('Broadcast error to a session:', error);
      }
    });
  }

  /**
   * 【新增】更新用户的字符使用量
   * 采用宽松模式：优先使用B后端API，失败时自动降级到KV存储
   * @param {string} username - 用户名
   * @param {number} charCount - 本次任务使用的字符数
   *
   * 核心逻辑：
   * - B后端API模式：调用B后端API进行配额扣除，失败时自动降级
   * - KV存储模式：使用本地KV存储进行扣除（降级方案）
   * - 宽松模式：因为音频已生成，扣除失败不应影响用户体验
   */
  async updateUserUsage(username, charCount) {
    if (!username || charCount <= 0) {
      return;
    }

    // 检查是否启用B后端API模式
    if (isBBackendApiEnabled(this.env)) {
      try {
        // 使用B后端API进行配额扣除
        console.log(`[USAGE-UPDATE] Using B Backend API for user ${username}, chars: ${charCount}`);

        const result = await callBBackendApi('/users/update-usage', {
          username: username,
          charCount: charCount
        }, this.env);

        console.log(`[USAGE-UPDATE] B Backend API update successful for user ${username}`);
        return result;
      } catch (error) {
        console.error(`[USAGE-UPDATE] B Backend API failed for user ${username}:`, error.message);

        // 【新增】启动简单的后台重试，不影响用户体验
        console.log(`[USAGE-UPDATE] Starting background retry for user ${username}, chars: ${charCount}`);
        this.startBackgroundQuotaRetry(username, charCount);

        // 配额扣除采用宽松模式：API失败时自动降级到KV存储
        // 因为音频已经生成，扣除失败不应该影响用户体验
        console.log(`[USAGE-UPDATE] Auto-fallback to KV storage mode for user ${username}`);
      }
    }

    // KV存储模式（原有逻辑或降级方案）
    console.log(`[USAGE-UPDATE] Using KV storage mode for user ${username}`);
    return await this.updateUserUsageKVMode(username, charCount);
  }

  /**
   * 【新增】启动简单的后台配额重试
   * 使用setTimeout进行异步重试，不阻塞主流程
   * @param {string} username - 用户名
   * @param {number} charCount - 字符数
   */
  startBackgroundQuotaRetry(username, charCount) {
    // 简单的后台重试：3次重试，间隔30秒、60秒、120秒
    const retryDelays = [30000, 60000, 120000]; // 30s, 60s, 120s

    retryDelays.forEach((delay, index) => {
      setTimeout(async () => {
        try {
          console.log(`[QUOTA-RETRY] Attempt ${index + 1}/3 for user ${username}, chars: ${charCount}`);

          const result = await callBBackendApi('/users/update-usage', {
            username: username,
            charCount: charCount
          }, this.env);

          console.log(`[QUOTA-RETRY] Success on attempt ${index + 1} for user ${username}`);
          // 成功后不再执行后续重试
          return result;

        } catch (error) {
          console.error(`[QUOTA-RETRY] Attempt ${index + 1} failed for user ${username}:`, error.message);

          if (index === retryDelays.length - 1) {
            console.error(`[QUOTA-RETRY] All retry attempts failed for user ${username}`);
          }
        }
      }, delay);
    });
  }

  /**
   * KV存储模式的用户使用量更新（原有逻辑）
   * @param {string} username - 用户名
   * @param {number} charCount - 本次任务使用的字符数
   */
  async updateUserUsageKVMode(username, charCount) {
    try {
      const userKey = `user:${username}`;
      // 注意：直接从 env.USERS 获取最新数据，而不是依赖可能过时的 taskData
      const userDataString = await this.env.USERS.get(userKey);
      if (!userDataString) {
        console.error(`[USAGE-UPDATE] User not found: ${username}`);
        return;
      }

      const userData = JSON.parse(userDataString);

      // --- START: 新增逻辑 ---
      // 更新VIP套餐内的已用配额（仅对受配额限制的用户）
      if (userData.vip && userData.vip.quotaChars !== undefined) {
        // 只有受配额限制的用户才更新已用配额
        // 真正的老用户（quotaChars为undefined）不更新，保持无限字符权益
        if (userData.vip.usedChars === undefined) {
          userData.vip.usedChars = 0;
        }
        userData.vip.usedChars += charCount;
        console.log(`[USAGE-UPDATE] Updated quota usage for user ${username}: ${userData.vip.usedChars}/${userData.vip.quotaChars}`);
      } else if (userData.vip) {
        console.log(`[USAGE-UPDATE] Legacy user ${username} - skipping quota tracking`);
      }
      // --- END: 新增逻辑 ---

      // 初始化 usage 对象（向后兼容老用户）
      if (!userData.usage) {
        userData.usage = {
          totalChars: 0,
          monthlyChars: 0,
          monthlyResetAt: getNextMonthResetTimestamp()
        };
      }

      const now = Date.now();
      // 检查月度数据是否需要重置
      if (now >= userData.usage.monthlyResetAt) {
        console.log(`[USAGE-UPDATE] Resetting monthly usage for user: ${username}`);
        userData.usage.monthlyChars = 0;
        userData.usage.monthlyResetAt = getNextMonthResetTimestamp();
      }

      // 累加字符数
      userData.usage.totalChars += charCount;
      userData.usage.monthlyChars += charCount;

      // 将更新后的用户数据写回 KV
      await this.env.USERS.put(userKey, JSON.stringify(userData));

      console.log(`[USAGE-UPDATE] Successfully updated usage for ${username}. Added: ${charCount}, VIP Used: ${userData.vip?.usedChars || 0}, Monthly total: ${userData.usage.monthlyChars}`);

    } catch (error) {
      console.error(`[USAGE-UPDATE] Failed to update usage for user ${username}:`, error);
      // 即使更新失败，也不应该影响主任务流程，所以只记录错误
    }
  }

  // 【新增】智能进度消息广播方法
  // 根据环境变量控制是否发送详细进度消息
  broadcastProgress(message) {
    const progressConfig = getProgressConfig(this.env);

    // 如果启用了进度消息，则发送
    if (progressConfig.ENABLE_PROGRESS_MESSAGES) {
      this.broadcast({ type: 'progress', message });
    }

    // 如果启用了调试模式，总是在控制台输出
    if (progressConfig.ENABLE_DEBUG_PROGRESS) {
      // 【使用新的日志系统】统一格式的进度日志
      this.logger.debug(message, { type: 'progress' }, this.logContext);
    }
  }
}

// ========== Utils 函数 ==========
// 从 utils.js

/**
 * 解析 HTTP Range 请求头
 * @param {string} header - Range 请求头的值 (e.g., "bytes=0-1023")
 * @param {number} totalSize - 文件的总大小
 * @returns {{start: number, end: number, length: number}|null} - 解析后的范围对象或 null
 */
function parseRange(header, totalSize) {
  if (!header) return null;

  const match = header.match(/bytes=(\d+)-(\d*)/);
  if (!match) {
    return null;
  }

  const start = parseInt(match[1], 10);
  // 如果末尾为空（如 "bytes=100-"），则表示到文件末尾
  const end = match[2] ? parseInt(match[2], 10) : totalSize - 1;

  // 范围有效性检查
  if (isNaN(start) || isNaN(end) || start > end || start >= totalSize) {
    return null;
  }

  const length = end - start + 1;
  return { start, end, length };
}

/**
 * 高效处理音频下载请求，支持 Range 请求（断点续传）
 * @param {Request} request - 原始请求对象
 * @param {object} env - Cloudflare Worker 的环境对象
 * @param {string} username - 用户名（用于日志记录）
 * @returns {Promise<Response>}
 */
async function handleDownload(request, env, username) {
  const url = new URL(request.url);
  const taskId = url.pathname.split('/download/')[1];

  if (env.DEBUG) {
    console.log(`[DOWNLOAD] 📥 Received Worker proxy download request (backup method):`, {
      taskId: taskId,
      username: username,
      url: request.url,
      method: request.method,
      downloadMethod: 'WORKER_PROXY_BACKUP',
      note: 'This is backup download method, R2 direct link is preferred',
      headers: Object.fromEntries(request.headers.entries()),
      timestamp: new Date().toISOString()
    });
  }

  if (!taskId) {
    console.error(`[DOWNLOAD] ❌ Missing task ID in download request`);
    return new Response(JSON.stringify({ error: 'Task ID is required' }), {
      status: 400,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  // 检查任务状态
  if (env.DEBUG) {
    console.log(`[DOWNLOAD] 📋 Checking task status for download: ${taskId}`);
  }
  const taskStatus = await getStatusKV(env, taskId);

  if (!taskStatus || taskStatus.status !== 'complete') {
    console.warn(`[DOWNLOAD] ⚠️ Audio not ready for download: ${taskId}, status: ${taskStatus?.status || 'not_found'}`);
    return new Response(JSON.stringify({ error: 'Audio not ready or task not found' }), {
      status: 404,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  const key = `audios/${taskId}.mp3`;

  if (env.DEBUG) {
    console.log(`[DOWNLOAD] 📁 Fetching audio file from R2:`, {
      taskId: taskId,
      key: key,
      bucketName: 'AUDIOS',
      envAUDIOS: !!env.AUDIOS,
      envType: typeof env.AUDIOS
    });
  }

  // 1. 获取文件元数据 (大小) - 优化：使用head而不是get
  const objectMetadata = await env.AUDIOS.head(key);
  if (objectMetadata === null) {
    console.error(`[DOWNLOAD] ❌ Audio file not found in R2: ${taskId}`);
    return new Response(JSON.stringify({ error: 'Audio file not found' }), {
      status: 404,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }
  const totalSize = objectMetadata.size;

  if (env.DEBUG) {
    console.log(`[DOWNLOAD] ✅ Successfully found audio file:`, {
      taskId: taskId,
      fileSize: totalSize,
      fileSizeMB: (totalSize / 1024 / 1024).toFixed(2),
      contentType: 'audio/mpeg',
      timestamp: new Date().toISOString()
    });
  }

  // 2. 解析 Range 请求头
  const rangeHeader = request.headers.get('range');
  if (env.DEBUG) {
    console.log(`[DOWNLOAD] 📊 Request headers analysis:`, {
      taskId: taskId,
      rangeHeader: rangeHeader,
      hasRange: !!rangeHeader,
      userAgent: request.headers.get('User-Agent'),
      accept: request.headers.get('Accept')
    });
  }

  const range = parseRange(rangeHeader, totalSize);

  // 3. 根据是否存在 Range 决定响应方式
  if (range === null) {
    // --- 情况 A: 完整文件下载 ---
    if (env.DEBUG) {
      console.log(`[DOWNLOAD] 📤 Serving complete file for task ${taskId}, size: ${totalSize}`);
    }
    const object = await env.AUDIOS.get(key);
    if (object === null) {
        return new Response('Audio file could not be retrieved.', { status: 404, headers: corsHeaders() });
    }

    return new Response(object.body, {
      status: 200,
      headers: {
        ...corsHeaders(),
        'Content-Type': 'audio/mpeg',
        'Content-Disposition': `attachment; filename="${generateDateBasedFilename()}"`,
        'Content-Length': totalSize.toString(),
        'Accept-Ranges': 'bytes', // 告知浏览器支持范围请求
        'Cache-Control': 'public, max-age=3600' // 缓存1小时
      }
    });

  } else {
    // --- 情况 B: 部分文件下载 (Range 请求) - 优化：使用R2的原生Range支持 ---
    if (env.DEBUG) {
      console.log(`[DOWNLOAD] 📏 Processing Range request for task ${taskId}: bytes ${range.start}-${range.end}`);
    }
    const object = await env.AUDIOS.get(key, {
      range: { offset: range.start, length: range.length }
    });
     if (object === null) {
        return new Response('Audio file range could not be retrieved.', { status: 404, headers: corsHeaders() });
    }

    return new Response(object.body, {
      status: 206, // Partial Content
      headers: {
        ...corsHeaders(),
        'Content-Type': 'audio/mpeg',
        'Content-Length': range.length.toString(),
        'Content-Range': `bytes ${range.start}-${range.end}/${totalSize}`,
        'Accept-Ranges': 'bytes',
        'Content-Disposition': `attachment; filename="${generateDateBasedFilename()}"`,
        'Cache-Control': 'public, max-age=3600'
      }
    });
  }
}

// ========== KV 状态管理函数 ==========
// 任务状态在 KV 中的 TTL (秒)，24小时
const TASK_STATUS_TTL = 1 * 24 * 60 * 60;

/**
 * 将任务状态写入或覆盖到 KV
 * @param {object} env - The environment object.
 * @param {string} taskId - The ID of the task.
 * @param {object} data - The status data to store.
 */
async function storeStatusKV(env, taskId, data) {
  try {
    const key = `status:${taskId}`;
    const statusData = {
      ...data,
      updatedAt: Date.now()
    };

    if (env.DEBUG) {
      console.log(`[KV-STATUS] Storing status for task ${taskId}:`, {
        status: statusData.status,
        currentStep: statusData.currentStep,
        dataSize: JSON.stringify(statusData).length,
        key: key
      });
    }

    await env.TTS_STATUS.put(key, JSON.stringify(statusData), {
      expirationTtl: TASK_STATUS_TTL
    });

    if (env.DEBUG) {
      console.log(`[KV-STATUS] ✅ Successfully stored status for task ${taskId}`);
    }
  } catch (error) {
    console.error(`[KV-STATUS] ❌ Failed to store status for task ${taskId}:`, {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * 从 KV 读取任务状态
 * @param {object} env - The environment object.
 * @param {string} taskId - The ID of the task.
 * @returns {Promise<object|null>} - The status data object, or null if not found.
 */
async function getStatusKV(env, taskId) {
  try {
    const key = `status:${taskId}`;

    if (env.DEBUG) {
      console.log(`[KV-STATUS] Getting status for task ${taskId}:`, { key: key });
    }

    // .get(key, 'json') 会自动处理 JSON.parse 和 null 的情况
    const statusData = await env.TTS_STATUS.get(key, 'json');

    if (statusData) {
      if (env.DEBUG) {
        console.log(`[KV-STATUS] ✅ Successfully retrieved status for task ${taskId}:`, {
          status: statusData.status,
          currentStep: statusData.currentStep,
          createdAt: statusData.createdAt ? new Date(statusData.createdAt).toISOString() : 'unknown'
        });
      }
    } else {
      if (env.DEBUG) {
        console.log(`[KV-STATUS] ⚠️ Status not found for task ${taskId}`);
      }
    }

    return statusData;
  } catch (error) {
    console.error(`[KV-STATUS] ❌ Failed to get status for task ${taskId}:`, {
      error: error.message,
      stack: error.stack
    });
    return null;
  }
}



export async function generateToken(username, env, isRefreshToken = false) {
  const AUTH_CONFIG = getAuthConfig(env);
  const expireTime = isRefreshToken ?
    AUTH_CONFIG.REFRESH_TOKEN_EXPIRE :
    AUTH_CONFIG.ACCESS_TOKEN_EXPIRE;

  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    sub: username,
    exp: Date.now() + expireTime * 1000,
    type: isRefreshToken ? 'refresh' : 'access'
  }));
  const signature = btoa(
    await hmacSha256(`${header}.${payload}`, AUTH_CONFIG.JWT_SECRET)
  );
  return `${header}.${payload}.${signature}`;
}

// ========== 主后端API调用基础设施 ==========

/**
 * 通用主后端API调用函数
 * @param {string} endpoint - API端点路径（如 '/auth/login'）
 * @param {object|null} data - 请求数据，null表示GET请求
 * @param {string|null} token - 认证令牌
 * @param {object} env - 环境变量
 * @returns {Promise<object>} API响应数据
 */
async function callMainBackendApi(endpoint, data = null, token = null, env) {
  const url = `${env.MAIN_BACKEND_BASE}/api${endpoint}`;
  const options = {
    method: data ? 'POST' : 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  if (token) {
    options.headers['Authorization'] = `Bearer ${token}`;
  }

  if (data) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || `HTTP ${response.status}`);
  }

  return await response.json();
}

/**
 * 带重试机制的主后端API调用函数
 * @param {string} endpoint - API端点路径
 * @param {object|null} data - 请求数据
 * @param {string|null} token - 认证令牌
 * @param {object} env - 环境变量
 * @param {number} maxRetries - 最大重试次数
 * @returns {Promise<object>} API响应数据
 */
async function callMainBackendApiWithRetry(endpoint, data = null, token = null, env, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await callMainBackendApi(endpoint, data, token, env);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1))); // 指数退避
    }
  }
}

// ========== B后端API基础设施 ==========

/**
 * B后端API调用函数（用于配额管理）
 * @param {string} endpoint - API端点路径（如：/users/check-quota）
 * @param {object|null} data - 请求数据
 * @param {object} env - 环境变量
 * @returns {Promise<object>} API响应数据
 */
async function callBBackendApi(endpoint, data = null, env) {
  if (!env.MAIN_BACKEND_BASE || !env.B_BACKEND_API_TOKEN) {
    throw new Error('B后端API配置缺失：需要MAIN_BACKEND_BASE和B_BACKEND_API_TOKEN环境变量');
  }

  const url = `${env.MAIN_BACKEND_BASE}/api/b-backend${endpoint}`;
  const options = {
    method: data ? 'POST' : 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${env.B_BACKEND_API_TOKEN}`
    }
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}`;
    try {
      const error = await response.json();
      errorMessage = error.error || errorMessage;
    } catch (parseError) {
      // 如果无法解析错误响应，使用默认错误信息
    }
    throw new Error(errorMessage);
  }

  return await response.json();
}

/**
 * 带重试机制的B后端API调用函数
 * @param {string} endpoint - API端点路径
 * @param {object|null} data - 请求数据
 * @param {object} env - 环境变量
 * @param {number} maxRetries - 最大重试次数
 * @returns {Promise<object>} API响应数据
 */
async function callBBackendApiWithRetry(endpoint, data = null, env, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await callBBackendApi(endpoint, data, env);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1))); // 指数退避
    }
  }
}

/**
 * 检查B后端API是否可用
 * @param {object} env - 环境变量
 * @returns {boolean} 是否可用
 */
function isBBackendApiEnabled(env) {
  return env.MAIN_BACKEND_BASE &&
         env.MAIN_BACKEND_BASE.trim() !== '' &&
         env.B_BACKEND_API_TOKEN &&
         env.B_BACKEND_API_TOKEN.trim() !== '';
}

/**
 * 检查配额验证是否允许降级到KV存储
 * @param {object} env - 环境变量
 * @returns {boolean} 是否允许降级
 */
function isQuotaFallbackEnabled(env) {
  return env.QUOTA_FALLBACK_ENABLED === 'true';
}

/**
 * 检查是否启用主后端API模式
 * @param {object} env - 环境变量
 * @returns {boolean} 是否启用主后端API
 */
function isMainBackendEnabled(env) {
  return env.MAIN_BACKEND_BASE && env.MAIN_BACKEND_BASE.trim() !== '';
}

/**
 * 【新增】验证配额管理相关的配置
 * 提供配置状态的详细信息，便于运维人员了解当前模式
 * @param {object} env - 环境变量
 * @returns {object} 配置验证结果
 */
function validateQuotaConfiguration(env) {
  const hasMainBackend = env.MAIN_BACKEND_BASE && env.MAIN_BACKEND_BASE.trim() !== '';
  const hasBBackendToken = env.B_BACKEND_API_TOKEN && env.B_BACKEND_API_TOKEN.trim() !== '';

  const result = {
    hasMainBackend,
    hasBBackendToken,
    isBBackendApiEnabled: hasMainBackend && hasBBackendToken,
    mode: 'unknown',
    warnings: []
  };

  // 确定运行模式
  if (hasMainBackend && hasBBackendToken) {
    result.mode = 'strict'; // 严格模式：配额验证使用B后端API
  } else if (!hasMainBackend && !hasBBackendToken) {
    result.mode = 'kv_only'; // 纯KV模式：所有功能使用KV存储
  } else if (hasMainBackend && !hasBBackendToken) {
    result.mode = 'hybrid'; // 混合模式：认证使用主后端，配额使用KV
    result.warnings.push('MAIN_BACKEND_BASE已配置但B_BACKEND_API_TOKEN缺失，配额管理将使用KV存储模式');
  } else if (!hasMainBackend && hasBBackendToken) {
    result.mode = 'invalid'; // 无效配置：有Token但无主后端URL
    result.warnings.push('B_BACKEND_API_TOKEN已配置但MAIN_BACKEND_BASE缺失，Token将被忽略');
  }

  return result;
}

// ========== 主后端API认证函数 ==========

/**
 * 用户登录（主后端API版本）
 * @param {string} username - 用户名或邮箱
 * @param {string} password - 密码
 * @param {object} env - 环境变量
 * @returns {Promise<object>} 登录结果
 */
async function loginUserViaMainBackend(username, password, env) {
  try {
    const result = await callMainBackendApi('/auth/login', { username, password }, null, env);
    return {
      success: true,
      access_token: result.access_token,
      refresh_token: result.refresh_token,
      username: result.username,
      expires_in: result.expires_in
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * 用户注册 - 发送验证码（主后端API版本）
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @param {string} email - 邮箱
 * @param {object} env - 环境变量
 * @returns {Promise<object>} 发送结果
 */
async function sendRegistrationCodeViaMainBackend(username, password, email, env) {
  try {
    const result = await callMainBackendApi('/auth/send-verification', { username, password, email }, null, env);
    return { success: true, message: result.message, email: result.email };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * 用户注册 - 验证邮箱并完成注册（主后端API版本）
 * @param {string} username - 用户名
 * @param {string} email - 邮箱
 * @param {string} code - 验证码
 * @param {object} env - 环境变量
 * @returns {Promise<object>} 注册结果
 */
async function completeRegistrationViaMainBackend(username, email, code, env) {
  try {
    const result = await callMainBackendApi('/auth/verify-email', { username, email, code }, null, env);
    return {
      success: true,
      access_token: result.access_token,
      refresh_token: result.refresh_token,
      username: result.username,
      expires_in: result.expires_in
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * 修改密码（主后端API版本）
 * @param {string} currentPassword - 当前密码
 * @param {string} newPassword - 新密码
 * @param {string} token - 认证令牌
 * @param {object} env - 环境变量
 * @returns {Promise<object>} 修改结果
 */
async function changePasswordViaMainBackend(currentPassword, newPassword, token, env) {
  try {
    const result = await callMainBackendApi('/auth/change-password', { currentPassword, newPassword }, token, env);
    return { success: true, message: result.message };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * 忘记密码 - 发送重置验证码（主后端API版本）
 * @param {string} email - 邮箱
 * @param {object} env - 环境变量
 * @returns {Promise<object>} 发送结果
 */
async function sendPasswordResetCodeViaMainBackend(email, env) {
  try {
    const result = await callMainBackendApi('/auth/forgot-password', { email }, null, env);
    return { success: true, message: result.message, email: result.email };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * 重置密码（主后端API版本）
 * @param {string} email - 邮箱
 * @param {string} code - 验证码
 * @param {string} newPassword - 新密码
 * @param {object} env - 环境变量
 * @returns {Promise<object>} 重置结果
 */
async function resetPasswordViaMainBackend(email, code, newPassword, env) {
  try {
    const result = await callMainBackendApi('/auth/reset-password', { email, code, newPassword }, null, env);
    return { success: true, message: result.message };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * 获取用户配额信息（主后端API版本）
 * @param {string} token - 认证令牌
 * @param {object} env - 环境变量
 * @returns {Promise<object>} 配额信息
 */
async function getUserQuotaViaMainBackend(token, env) {
  try {
    const result = await callMainBackendApi('/user/quota', null, token, env);

    // 检查响应格式并进行转换
    const transformedResult = transformQuotaResponse(result, token);

    return {
      success: true,
      username: transformedResult.username,
      vip: transformedResult.vip,
      usage: transformedResult.usage
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * 转换配额响应格式，兼容主后端扁平化响应和KV存储嵌套格式
 * @param {object} response - 原始响应数据
 * @param {string} token - 用户token（用于提取用户名）
 * @returns {object} 标准化的配额信息
 */
function transformQuotaResponse(response, token) {
  // 检查是否已经是标准嵌套格式（KV存储格式）
  if (response.username && response.vip && response.usage) {
    console.log('[QUOTA-TRANSFORM] Response already in nested format (KV storage format)');
    return response;
  }

  // 处理主后端扁平化格式
  console.log('[QUOTA-TRANSFORM] Converting flat response to nested format (Main backend format)');
  console.log('[QUOTA-TRANSFORM] Original response keys:', Object.keys(response));

  // 从token中提取用户名（简单解析，不验证签名）
  let username = 'unknown';
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    username = payload.username || payload.sub || 'unknown';
    console.log('[QUOTA-TRANSFORM] Extracted username from token:', username);
  } catch (error) {
    console.warn('[QUOTA-TRANSFORM] Failed to extract username from token:', error.message);
  }

  // 转换为标准嵌套格式
  const transformedResponse = {
    username: username,
    vip: {
      type: response.type || null,
      expireAt: response.expireAt || 0,
      quotaChars: response.quotaChars,
      usedChars: response.usedChars,
      remainingChars: response.remainingChars,
      usagePercentage: response.usagePercentage || 0,
      isLegacyUser: response.isLegacyUser || false,
      isExpired: response.isExpired || false
    },
    usage: {
      totalChars: response.totalChars || 0,
      monthlyChars: response.monthlyChars || 0,
      monthlyResetAt: response.monthlyResetAt || getNextMonthResetTimestamp()
    }
  };

  console.log('[QUOTA-TRANSFORM] Transformed response structure:', {
    username: transformedResponse.username,
    vip: Object.keys(transformedResponse.vip),
    usage: Object.keys(transformedResponse.usage)
  });

  return transformedResponse;
}

/**
 * 将嵌套结构的配额响应转换为前端期望的扁平结构
 * @param {object} nestedResponse - 嵌套结构的响应数据
 * @returns {object} 扁平结构的响应数据
 */
function convertToFlatResponse(nestedResponse) {
  console.log('[RESPONSE-CONVERT] Converting nested response to flat structure for frontend');
  console.log('[RESPONSE-CONVERT] Input structure:', {
    hasUsername: !!nestedResponse.username,
    hasVip: !!nestedResponse.vip,
    hasUsage: !!nestedResponse.usage
  });

  const vip = nestedResponse.vip || {};
  const usage = nestedResponse.usage || {};
  const now = Date.now();

  // 计算isVip状态
  const isVip = !!(vip.type && vip.expireAt && now < vip.expireAt);

  // 计算剩余时间字符串
  let remainingTime = null;
  if (isVip && vip.expireAt) {
    const remainingMs = vip.expireAt - now;
    const remainingDays = Math.ceil(remainingMs / (24 * 60 * 60 * 1000));
    remainingTime = remainingDays > 0 ? `${remainingDays}天` : null;
  }

  // 处理老用户的特殊情况
  const isLegacyUser = vip.isLegacyUser || false;
  const quotaChars = isLegacyUser ? undefined : vip.quotaChars;
  const usedChars = isLegacyUser ? undefined : vip.usedChars;
  const remainingChars = isLegacyUser ? undefined : vip.remainingChars;

  // 构建前端期望的扁平结构
  const flatResponse = {
    // 原有字段（保持向后兼容）
    isVip: isVip,
    expireAt: vip.expireAt || 0,
    type: vip.type || null,
    remainingTime: remainingTime,

    // 新增配额相关字段（老用户为undefined，新用户有具体值）
    quotaChars: quotaChars,
    usedChars: usedChars,
    remainingChars: remainingChars,
    usagePercentage: vip.usagePercentage || 0,
    isLegacyUser: isLegacyUser,

    // 保留原始嵌套数据（用于调试和向后兼容）
    _debug: {
      username: nestedResponse.username,
      originalVip: vip,
      originalUsage: usage
    }
  };

  console.log('[RESPONSE-CONVERT] Output flat structure:', {
    isVip: flatResponse.isVip,
    expireAt: flatResponse.expireAt,
    type: flatResponse.type,
    hasQuotaChars: flatResponse.quotaChars !== undefined,
    isLegacyUser: flatResponse.isLegacyUser
  });

  return flatResponse;
}

/**
 * 使用卡密激活VIP（主后端API版本）
 * @param {string} code - 卡密
 * @param {string} token - 认证令牌
 * @param {object} env - 环境变量
 * @returns {Promise<object>} 激活结果
 */
async function useCardViaMainBackend(code, token, env) {
  try {
    const result = await callMainBackendApi('/card/use', { code }, token, env);
    return {
      success: true,
      quota: result.quota
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

export async function verifyToken(token, env, allowRefresh = false) {
  const AUTH_CONFIG = getAuthConfig(env);
  try {
    const [header, payload, signature] = token.split('.');
    const expectedSignature = btoa(
      await hmacSha256(`${header}.${payload}`, AUTH_CONFIG.JWT_SECRET)
    );

    if (signature !== expectedSignature) {
      throw new Error('Invalid signature');
    }

    const decoded = JSON.parse(atob(payload));

    // 检查 token 类型
    if (!allowRefresh && decoded.type === 'refresh') {
      throw new Error('Invalid token type');
    }

    if (Date.now() > decoded.exp) {
      throw new Error('Token expired');
    }

    return decoded.sub;
  } catch (error) {
    throw new Error('Invalid token');
  }
}

export async function bcrypt(password, env) {
  const AUTH_CONFIG = getAuthConfig(env);
  const encoder = new TextEncoder();
  const data = encoder.encode(password + AUTH_CONFIG.JWT_SECRET);
  const hash = await crypto.subtle.digest('SHA-256', data);
  return btoa(String.fromCharCode(...new Uint8Array(hash)));
}

async function hmacSha256(message, key) {
  const encoder = new TextEncoder();
  const keyData = await crypto.subtle.importKey(
    'raw',
    encoder.encode(key),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  const signature = await crypto.subtle.sign(
    'HMAC',
    keyData,
    encoder.encode(message)
  );
  return String.fromCharCode(...new Uint8Array(signature));
}

// 腾讯云 API 签名相关函数
async function sha256Hex(message) {
  const encoder = new TextEncoder();
  const data = encoder.encode(message);
  const hash = await crypto.subtle.digest('SHA-256', data);
  return Array.from(new Uint8Array(hash))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

async function hmacSha256Hex(message, key) {
  const encoder = new TextEncoder();
  const keyData = await crypto.subtle.importKey(
    'raw',
    typeof key === 'string' ? encoder.encode(key) : key,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  const signature = await crypto.subtle.sign(
    'HMAC',
    keyData,
    encoder.encode(message)
  );
  return Array.from(new Uint8Array(signature))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

async function hmacSha256Binary(message, key) {
  const encoder = new TextEncoder();
  const keyData = await crypto.subtle.importKey(
    'raw',
    typeof key === 'string' ? encoder.encode(key) : key,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  const signature = await crypto.subtle.sign(
    'HMAC',
    keyData,
    encoder.encode(message)
  );
  return new Uint8Array(signature);
}

// 腾讯云 API v3 签名算法
async function generateTencentCloudSignature(secretId, secretKey, service, region, action, payload, timestamp) {
  const date = new Date(timestamp * 1000).toISOString().substring(0, 10);

  // 步骤 1: 拼接规范请求串
  const httpRequestMethod = 'POST';
  const canonicalUri = '/';
  const canonicalQueryString = '';
  const canonicalHeaders = `content-type:application/json; charset=utf-8\nhost:${service}.tencentcloudapi.com\nx-tc-action:${action.toLowerCase()}\n`;
  const signedHeaders = 'content-type;host;x-tc-action';
  const hashedRequestPayload = await sha256Hex(payload);

  const canonicalRequest = [
    httpRequestMethod,
    canonicalUri,
    canonicalQueryString,
    canonicalHeaders,
    signedHeaders,
    hashedRequestPayload
  ].join('\n');

  // 步骤 2: 拼接待签名字符串
  const algorithm = 'TC3-HMAC-SHA256';
  const requestTimestamp = timestamp.toString();
  const credentialScope = `${date}/${service}/tc3_request`;
  const hashedCanonicalRequest = await sha256Hex(canonicalRequest);

  const stringToSign = [
    algorithm,
    requestTimestamp,
    credentialScope,
    hashedCanonicalRequest
  ].join('\n');

  // 步骤 3: 计算签名
  const secretDate = await hmacSha256Binary(date, `TC3${secretKey}`);
  const secretService = await hmacSha256Binary(service, secretDate);
  const secretSigning = await hmacSha256Binary('tc3_request', secretService);
  const signature = await hmacSha256Hex(stringToSign, secretSigning);

  // 步骤 4: 拼接 Authorization
  const authorization = `${algorithm} Credential=${secretId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

  return {
    authorization,
    timestamp: requestTimestamp,
    hashedRequestPayload
  };
}

// 调用腾讯云 SES API 发送邮件
async function sendEmailViaTencentSES(toEmail, templateData, env) {
  const sesConfig = getSESConfig(env);
  const service = 'ses';
  const action = 'SendEmail';
  const version = '2020-10-02';
  const timestamp = Math.floor(Date.now() / 1000);

  // 调试日志
  console.log('SES Config check:', {
    hasSecretId: !!sesConfig.TENCENT_SECRET_ID,
    hasSecretKey: !!sesConfig.TENCENT_SECRET_KEY,
    region: sesConfig.SES_REGION,
    fromEmail: sesConfig.FROM_EMAIL,
    templateId: sesConfig.VERIFICATION_TEMPLATE_ID
  });

  // 构建请求体
  const payload = JSON.stringify({
    FromEmailAddress: `${sesConfig.FROM_EMAIL_NAME} <${sesConfig.FROM_EMAIL}>`,
    Destination: [toEmail],
    Subject: '邮箱验证码',
    Template: {
      TemplateID: parseInt(sesConfig.VERIFICATION_TEMPLATE_ID),
      TemplateData: JSON.stringify(templateData)
    },
    TriggerType: 1 // 触发类邮件
  });

  // 生成签名
  const signatureInfo = await generateTencentCloudSignature(
    sesConfig.TENCENT_SECRET_ID,
    sesConfig.TENCENT_SECRET_KEY,
    service,
    sesConfig.SES_REGION,
    action,
    payload,
    timestamp
  );

  // 构建请求头
  const headers = {
    'Authorization': signatureInfo.authorization,
    'Content-Type': 'application/json; charset=utf-8',
    'Host': `${service}.tencentcloudapi.com`,
    'X-TC-Action': action,
    'X-TC-Timestamp': signatureInfo.timestamp,
    'X-TC-Version': version,
    'X-TC-Region': sesConfig.SES_REGION
  };

  // 发送请求
  const response = await fetch(`https://${service}.tencentcloudapi.com/`, {
    method: 'POST',
    headers: headers,
    body: payload
  });

  const result = await response.json();

  if (!response.ok || result.Response.Error) {
    throw new Error(result.Response.Error?.Message || 'Failed to send email');
  }

  return result.Response.MessageId;
}

// 验证码相关函数
function generateVerificationCode() {
  return Math.floor(100000 + Math.random() * 900000).toString(); // 6位数字验证码
}

async function storeVerificationCode(email, code, env) {
  const expireTime = Date.now() + 10 * 60 * 1000; // 10分钟过期
  const verificationData = {
    code: code,
    expireTime: expireTime,
    attempts: 0,
    maxAttempts: 5
  };

  // 存储验证码，key格式：verification:email:<EMAIL>
  await env.USERS.put(`verification:email:${email}`, JSON.stringify(verificationData), {
    expirationTtl: 600 // 10分钟TTL
  });
}

async function verifyEmailCode(email, inputCode, env) {
  const verificationData = await env.USERS.get(`verification:email:${email}`);

  if (!verificationData) {
    throw new Error('验证码不存在或已过期');
  }

  const data = JSON.parse(verificationData);

  // 检查是否过期
  if (Date.now() > data.expireTime) {
    await env.USERS.delete(`verification:email:${email}`);
    throw new Error('验证码已过期');
  }

  // 检查尝试次数
  if (data.attempts >= data.maxAttempts) {
    await env.USERS.delete(`verification:email:${email}`);
    throw new Error('验证码尝试次数过多，请重新获取');
  }

  // 验证码错误，增加尝试次数
  if (data.code !== inputCode) {
    data.attempts += 1;
    await env.USERS.put(`verification:email:${email}`, JSON.stringify(data), {
      expirationTtl: Math.max(0, Math.floor((data.expireTime - Date.now()) / 1000))
    });
    throw new Error('验证码错误');
  }

  // 验证成功，删除验证码
  await env.USERS.delete(`verification:email:${email}`);
  return true;
}

// 检查邮箱发送频率限制
async function checkEmailSendLimit(email, env) {
  const limitKey = `email_limit:${email}`;
  const lastSendTime = await env.USERS.get(limitKey);

  if (lastSendTime) {
    const timeDiff = Date.now() - parseInt(lastSendTime);
    if (timeDiff < 60000) { // 1分钟内不能重复发送
      const remainingTime = Math.ceil((60000 - timeDiff) / 1000);
      throw new Error(`请等待 ${remainingTime} 秒后再试`);
    }
  }

  // 记录发送时间
  await env.USERS.put(limitKey, Date.now().toString(), {
    expirationTtl: 60 // 1分钟TTL
  });
}

// 添加 refreshAccessToken 函数
async function refreshAccessToken(refreshToken, env) {
  try {
    const username = await verifyToken(refreshToken, env, true);
    const newAccessToken = await generateToken(username, env);
    // 生成新的 refresh token
    const newRefreshToken = await generateToken(username, env, true);

    return {
      token: newAccessToken,
      refresh_token: newRefreshToken,
      username,
      expires_in: getAuthConfig(env).ACCESS_TOKEN_EXPIRE
    };
  } catch (error) {
    throw new Error('Invalid refresh token');
  }
}

// ========== Auth 处理函数 ==========
// 从 auth.js
async function handleAuth(request, env) {
  const url = new URL(request.url);

  if (request.method === 'POST' && url.pathname === '/api/auth/login') {
    const { username, password } = await request.json();

    try {
      // 【新增】检查是否启用主后端API模式
      if (isMainBackendEnabled(env)) {
        // 使用主后端API进行登录
        const result = await loginUserViaMainBackend(username, password, env);

        if (result.success) {
          return new Response(JSON.stringify({
            access_token: result.access_token,
            refresh_token: result.refresh_token,
            username: result.username,
            expires_in: result.expires_in
          }), {
            status: 200,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        } else {
          return new Response(JSON.stringify({ error: result.error }), {
            status: 400,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        }
      }

      // 【保留】原有的KV存储登录逻辑（作为降级方案）
      let actualUsername = username;

      // 检查输入是否为邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (emailRegex.test(username)) {
        // 如果是邮箱，先查找对应的用户名
        const emailMapping = await env.USERS.get(`email:${username}`);
        if (!emailMapping) {
          return new Response(JSON.stringify({ error: '用户名或密码错误' }), {
            status: 400,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        }
        actualUsername = emailMapping;
      }

      const userData = await env.USERS.get(`user:${actualUsername}`);
      if (!userData) {
        return new Response(JSON.stringify({ error: '用户名或密码错误' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const user = JSON.parse(userData);
      const hashedPassword = await bcrypt(password, env);

      if (hashedPassword !== user.passwordHash) {
        return new Response(JSON.stringify({ error: '用户名或密码错误' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 使用实际的用户名生成token
      const accessToken = await generateToken(actualUsername, env);
      const refreshToken = await generateToken(actualUsername, env, true);

      return new Response(JSON.stringify({
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: getAuthConfig(env).ACCESS_TOKEN_EXPIRE
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      return new Response(JSON.stringify({ error: '登录失败' }), {
        status: 400,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 添加刷新 token 接口
  if (request.method === 'POST' && url.pathname === '/api/auth/refresh') {
    try {
      const { refresh_token } = await request.json();
      const { token, refresh_token: newRefreshToken, expires_in } = await refreshAccessToken(refresh_token, env);

      return new Response(JSON.stringify({
        access_token: token,
        refresh_token: newRefreshToken,
        expires_in: expires_in
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Invalid refresh token',
        message: error.message
      }), {
        status: 401,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  if (request.method === 'POST' && url.pathname === '/api/auth/register') {
    const { username, password } = await request.json();

    const existingUser = await env.USERS.get(`user:${username}`);
    if (existingUser) {
        return new Response(JSON.stringify({ error: '用户名已存在' }), {
            status: 400,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
    }

    const userData = {
        username,
        passwordHash: await bcrypt(password, env),
        createdAt: Date.now(),
        quota: {
            daily: 100,
            used: 0,
            resetAt: Date.now()
        },
        // 【新增】初始化VIP信息
        vip: {
            expireAt: 0,
            type: null,
            quotaChars: 0,    // 新用户初始配额为0
            usedChars: 0      // 新用户已用配额为0
        },
        // 【新增】初始化用量统计
        usage: {
            totalChars: 0,
            monthlyChars: 0,
            monthlyResetAt: getNextMonthResetTimestamp()
        }
    };

    // 先生成认证信息
    const accessToken = await generateToken(username, env);
    const refreshToken = await generateToken(username, env, true);

    // 存储用户数据
    await env.USERS.put(`user:${username}`, JSON.stringify(userData));

    // 返回与登录接口相同的数据结构
    return new Response(JSON.stringify({
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: getAuthConfig(env).ACCESS_TOKEN_EXPIRE
    }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  // 新增：发送邮箱验证码接口
  if (request.method === 'POST' && url.pathname === '/api/auth/send-verification') {
    try {
      const { email, username, password } = await request.json();

      // 【新增】检查是否启用主后端API模式
      if (isMainBackendEnabled(env)) {
        // 使用主后端API发送验证码
        const result = await sendRegistrationCodeViaMainBackend(username, password, email, env);

        if (result.success) {
          return new Response(JSON.stringify({
            message: result.message,
            email: result.email
          }), {
            status: 200,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        } else {
          return new Response(JSON.stringify({ error: result.error }), {
            status: 400,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        }
      }

      // 【保留】原有的KV存储逻辑（作为降级方案）
      // 基本参数验证
      if (!email || !username || !password) {
        return new Response(JSON.stringify({ error: '邮箱、用户名和密码不能为空' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return new Response(JSON.stringify({ error: '邮箱格式不正确' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查用户名是否已存在
      const existingUser = await env.USERS.get(`user:${username}`);
      if (existingUser) {
        return new Response(JSON.stringify({ error: '用户名已存在' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查邮箱是否已被注册
      const existingEmail = await env.USERS.get(`email:${email}`);
      if (existingEmail) {
        return new Response(JSON.stringify({ error: '该邮箱已被注册' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查发送频率限制
      await checkEmailSendLimit(email, env);

      // 生成验证码
      const verificationCode = generateVerificationCode();

      // 存储验证码
      await storeVerificationCode(email, verificationCode, env);

      // 临时存储用户注册信息（10分钟过期）
      const tempUserData = {
        username,
        passwordHash: await bcrypt(password, env),
        email,
        createdAt: Date.now()
      };
      await env.USERS.put(`pending:user:${username}`, JSON.stringify(tempUserData), {
        expirationTtl: 600 // 10分钟TTL
      });

      // 发送验证码邮件
      const templateData = {
        code: verificationCode,
        username: username
      };

      await sendEmailViaTencentSES(email, templateData, env);

      return new Response(JSON.stringify({
        message: '验证码已发送到您的邮箱，请查收',
        email: email
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Send verification error:', error);
      return new Response(JSON.stringify({
        error: error.message || '发送验证码失败，请稍后重试'
      }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 新增：验证邮箱并完成注册接口
  if (request.method === 'POST' && url.pathname === '/api/auth/verify-email') {
    try {
      const { username, email, code } = await request.json();

      // 【新增】检查是否启用主后端API模式
      if (isMainBackendEnabled(env)) {
        // 使用主后端API完成注册
        const result = await completeRegistrationViaMainBackend(username, email, code, env);

        if (result.success) {
          return new Response(JSON.stringify({
            access_token: result.access_token,
            refresh_token: result.refresh_token,
            username: result.username,
            expires_in: result.expires_in
          }), {
            status: 200,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        } else {
          return new Response(JSON.stringify({ error: result.error }), {
            status: 400,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        }
      }

      // 【保留】原有的KV存储逻辑（作为降级方案）
      // 基本参数验证
      if (!username || !email || !code) {
        return new Response(JSON.stringify({ error: '用户名、邮箱和验证码不能为空' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 验证验证码
      await verifyEmailCode(email, code, env);

      // 获取临时存储的用户数据
      const tempUserData = await env.USERS.get(`pending:user:${username}`);
      if (!tempUserData) {
        return new Response(JSON.stringify({ error: '注册信息已过期，请重新注册' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const userData = JSON.parse(tempUserData);

      // 验证邮箱是否匹配
      if (userData.email !== email) {
        return new Response(JSON.stringify({ error: '邮箱信息不匹配' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 再次检查用户名是否已存在（防止并发注册）
      const existingUser = await env.USERS.get(`user:${username}`);
      if (existingUser) {
        return new Response(JSON.stringify({ error: '用户名已存在' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 创建正式用户数据
      const finalUserData = {
        username: userData.username,
        passwordHash: userData.passwordHash,
        email: userData.email,
        emailVerified: true,
        createdAt: userData.createdAt,
        quota: {
          daily: 100,
          used: 0,
          resetAt: Date.now()
        },
        // 【新增】初始化VIP信息
        vip: {
          expireAt: 0,
          type: null,
          quotaChars: 0,    // 新用户初始配额为0
          usedChars: 0      // 新用户已用配额为0
        },
        // 【新增】初始化用量统计
        usage: {
          totalChars: 0,
          monthlyChars: 0,
          monthlyResetAt: getNextMonthResetTimestamp()
        }
      };

      // 生成认证token
      const accessToken = await generateToken(username, env);
      const refreshToken = await generateToken(username, env, true);

      // 保存用户数据和邮箱映射
      await env.USERS.put(`user:${username}`, JSON.stringify(finalUserData));
      await env.USERS.put(`email:${email}`, username); // 邮箱到用户名的映射

      // 清理临时数据
      await env.USERS.delete(`pending:user:${username}`);

      return new Response(JSON.stringify({
        message: '注册成功',
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: getAuthConfig(env).ACCESS_TOKEN_EXPIRE
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Verify email error:', error);
      return new Response(JSON.stringify({
        error: error.message || '验证失败，请重试'
      }), {
        status: 400,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 新增：修改密码接口
  if (request.method === 'POST' && url.pathname === '/api/auth/change-password') {
    try {
      const { currentPassword, newPassword } = await request.json();
      const token = request.headers.get('Authorization')?.replace('Bearer ', '');

      // 【新增】检查是否启用主后端API模式
      if (isMainBackendEnabled(env)) {
        // 使用主后端API修改密码
        const result = await changePasswordViaMainBackend(currentPassword, newPassword, token, env);

        if (result.success) {
          return new Response(JSON.stringify({
            message: result.message
          }), {
            status: 200,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        } else {
          return new Response(JSON.stringify({ error: result.error }), {
            status: 400,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        }
      }

      // 【保留】原有的KV存储逻辑（作为降级方案）
      // 验证请求参数
      if (!currentPassword || !newPassword) {
        return new Response(JSON.stringify({ error: '当前密码和新密码不能为空' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 验证新密码强度
      if (newPassword.length < 6) {
        return new Response(JSON.stringify({ error: '新密码长度不能少于6位' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 验证token
      if (!token) {
        return new Response(JSON.stringify({
          error: '未授权访问',
          code: 'NO_TOKEN'
        }), {
          status: 401,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const username = await verifyToken(token, env);

      // 获取用户数据
      const userData = await env.USERS.get(`user:${username}`);
      if (!userData) {
        return new Response(JSON.stringify({ error: '用户不存在' }), {
          status: 404,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const user = JSON.parse(userData);

      // 验证当前密码
      const currentPasswordHash = await bcrypt(currentPassword, env);
      if (currentPasswordHash !== user.passwordHash) {
        return new Response(JSON.stringify({ error: '当前密码错误' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查新密码是否与当前密码相同
      const newPasswordHash = await bcrypt(newPassword, env);
      if (newPasswordHash === user.passwordHash) {
        return new Response(JSON.stringify({ error: '新密码不能与当前密码相同' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 更新密码
      user.passwordHash = newPasswordHash;
      user.passwordUpdatedAt = Date.now();

      // 保存更新后的用户数据
      await env.USERS.put(`user:${username}`, JSON.stringify(user));

      return new Response(JSON.stringify({
        message: '密码修改成功'
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Change password error:', error);

      // 【关键修改】处理token验证失败，使用统一的认证错误处理函数
      if (isAuthError(error)) {
        return createAuthErrorResponse(error);
      }

      return new Response(JSON.stringify({
        error: error.message || '修改密码失败，请重试'
      }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 新增：忘记密码 - 发送重置验证码接口
  if (request.method === 'POST' && url.pathname === '/api/auth/forgot-password') {
    try {
      const { email } = await request.json();

      // 【新增】检查是否启用主后端API模式
      if (isMainBackendEnabled(env)) {
        // 使用主后端API发送重置验证码
        const result = await sendPasswordResetCodeViaMainBackend(email, env);

        if (result.success) {
          return new Response(JSON.stringify({
            message: result.message,
            email: result.email
          }), {
            status: 200,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        } else {
          return new Response(JSON.stringify({ error: result.error }), {
            status: 400,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        }
      }

      // 【保留】原有的KV存储逻辑（作为降级方案）
      // 基本参数验证
      if (!email) {
        return new Response(JSON.stringify({ error: '邮箱不能为空' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return new Response(JSON.stringify({ error: '邮箱格式不正确' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查邮箱是否已注册
      const emailMapping = await env.USERS.get(`email:${email}`);
      if (!emailMapping) {
        return new Response(JSON.stringify({ error: '该邮箱未注册' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查发送频率限制
      await checkEmailSendLimit(email, env);

      // 生成验证码
      const verificationCode = generateVerificationCode();

      // 存储重置密码验证码（使用不同的key前缀）
      await env.USERS.put(`reset:email:${email}`, JSON.stringify({
        code: verificationCode,
        expireTime: Date.now() + 10 * 60 * 1000, // 10分钟过期
        attempts: 0,
        maxAttempts: 5,
        username: emailMapping
      }), {
        expirationTtl: 600 // 10分钟TTL
      });

      // 发送验证码邮件
      const templateData = {
        code: verificationCode,
        username: emailMapping
      };

      await sendEmailViaTencentSES(email, templateData, env);

      return new Response(JSON.stringify({
        message: '重置密码验证码已发送到您的邮箱，请查收',
        email: email
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Forgot password error:', error);
      return new Response(JSON.stringify({
        error: error.message || '发送验证码失败，请稍后重试'
      }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 新增：重置密码接口
  if (request.method === 'POST' && url.pathname === '/api/auth/reset-password') {
    try {
      const { email, code, newPassword } = await request.json();

      // 【新增】检查是否启用主后端API模式
      if (isMainBackendEnabled(env)) {
        // 使用主后端API重置密码
        const result = await resetPasswordViaMainBackend(email, code, newPassword, env);

        if (result.success) {
          return new Response(JSON.stringify({
            message: result.message
          }), {
            status: 200,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        } else {
          return new Response(JSON.stringify({ error: result.error }), {
            status: 400,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        }
      }

      // 【保留】原有的KV存储逻辑（作为降级方案）
      // 基本参数验证
      if (!email || !code || !newPassword) {
        return new Response(JSON.stringify({ error: '邮箱、验证码和新密码不能为空' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 验证新密码强度
      if (newPassword.length < 6) {
        return new Response(JSON.stringify({ error: '新密码长度不能少于6位' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 获取重置密码验证码数据
      const resetData = await env.USERS.get(`reset:email:${email}`);
      if (!resetData) {
        return new Response(JSON.stringify({ error: '验证码不存在或已过期' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const data = JSON.parse(resetData);

      // 检查是否过期
      if (Date.now() > data.expireTime) {
        await env.USERS.delete(`reset:email:${email}`);
        return new Response(JSON.stringify({ error: '验证码已过期' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查尝试次数
      if (data.attempts >= data.maxAttempts) {
        await env.USERS.delete(`reset:email:${email}`);
        return new Response(JSON.stringify({ error: '验证码尝试次数过多，请重新获取' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 验证码错误，增加尝试次数
      if (data.code !== code) {
        data.attempts += 1;
        await env.USERS.put(`reset:email:${email}`, JSON.stringify(data), {
          expirationTtl: Math.max(0, Math.floor((data.expireTime - Date.now()) / 1000))
        });
        return new Response(JSON.stringify({ error: '验证码错误' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 获取用户数据
      const username = data.username;
      const userData = await env.USERS.get(`user:${username}`);
      if (!userData) {
        return new Response(JSON.stringify({ error: '用户不存在' }), {
          status: 404,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const user = JSON.parse(userData);

      // 检查新密码是否与当前密码相同
      const newPasswordHash = await bcrypt(newPassword, env);
      if (newPasswordHash === user.passwordHash) {
        return new Response(JSON.stringify({ error: '新密码不能与当前密码相同' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 更新密码
      user.passwordHash = newPasswordHash;
      user.passwordUpdatedAt = Date.now();

      // 保存更新后的用户数据
      await env.USERS.put(`user:${username}`, JSON.stringify(user));

      // 删除重置验证码
      await env.USERS.delete(`reset:email:${email}`);

      return new Response(JSON.stringify({
        message: '密码重置成功，请使用新密码登录'
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Reset password error:', error);
      return new Response(JSON.stringify({
        error: error.message || '重置密码失败，请重试'
      }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  return new Response('Not Found', { status: 404, headers: corsHeaders() });
}

// ========== TTS 处理函数 ==========
// 从 tts.js
/**
 * 【SSML增强版】智能分割文本，支持SSML指令识别
 * 这个函数会将文本分割成不超过maxLength的块，同时确保 [...] 形式的SSML指令不会被破坏。
 * 保持现有的所有智能分割和性能优化逻辑。
 * @param {string} text - 输入的文本，可能包含SSML指令
 * @returns {Promise<string[]>} - 分割后的文本块数组
 */
async function splitText(text) {
  const maxLength = 990;  // 每个片段的最大长度，保持与原版一致

  // 【SSML功能】检测文本中是否包含SSML指令
  const hasSSMLDirectives = /\[.*?\]/.test(text);

  if (hasSSMLDirectives) {
    // 【SSML处理路径】使用SSML感知的分割逻辑
    console.log('[SSML-SPLIT] Detected SSML directives, using SSML-aware splitting');
    return await splitTextWithSSML(text, maxLength);
  } else {
    // 【传统处理路径】使用原有的智能分割逻辑，保持100%兼容性
    return await splitTextTraditional(text, maxLength);
  }
}

/**
 * 【新增】SSML感知的文本分割函数
 * 确保SSML指令（如[calmly], [whispering]）不会被分割破坏
 */
async function splitTextWithSSML(text, maxLength) {
  // 使用正则表达式将文本分割成普通文本片段和SSML指令片段的数组
  // 例如 "[calmly] Hello world. [whispering] Secret." 会被分割成:
  // ["", "[calmly]", " Hello world. ", "[whispering]", " Secret."]
  const parts = text.split(/(\[.*?\])/g).filter(Boolean); // filter(Boolean) 用于移除空字符串

  const chunks = [];
  let currentChunk = "";

  for (const part of parts) {
    // 如果当前块加上新片段会超过最大长度
    if (currentChunk.length + part.length > maxLength) {
      // 如果当前块有内容，则推入chunks数组
      if (currentChunk.trim()) {
        chunks.push(currentChunk.trim());
      }

      // 如果单个片段本身就超长，需要进一步处理
      if (part.length > maxLength) {
        // 检查是否是SSML指令
        if (/^\[.*?\]$/.test(part.trim())) {
          // 如果是SSML指令但超长，保持完整（这种情况极少见）
          console.warn('[SSML-SPLIT] Warning: SSML directive exceeds maxLength:', part.substring(0, 50) + '...');
          chunks.push(part.trim());
          currentChunk = "";
        } else {
          // 如果是普通文本超长，使用智能分割
          const subChunks = smartSplitLongText(part, maxLength);
          chunks.push(...subChunks);
          currentChunk = "";
        }
      } else {
        // 开始一个新的块
        currentChunk = part;
      }
    } else {
      // 否则，将新片段添加到当前块
      currentChunk += part;
    }
  }

  // 推入最后一个块
  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  // 如果没有任何内容，返回原文本作为单个块
  if (chunks.length === 0 && text.length > 0) {
    chunks.push(text);
  }

  console.log(`[SSML-SPLIT] Split text with SSML into ${chunks.length} chunks`);
  return chunks;
}

/**
 * 【保持原有】传统的智能分割函数，保持100%向后兼容
 * 这是原有的splitText函数逻辑，确保非SSML文本的处理完全不变
 */
async function splitTextTraditional(text, maxLength) {
  let sentences;
  try {
    const segmenter = new Intl.Segmenter(['en', 'zh'], { granularity: 'sentence' });
    const iterator = segmenter.segment(text);
    sentences = Array.from(iterator).map(s => s.segment);
  } catch (e) {
    // 如果 Intl.Segmenter 在极端情况下失败或环境不支持，回退到正则表达式方案
    console.error("Intl.Segmenter failed, falling back to regex:", e);
    const sentencePattern = /(?<=[。！？!?；;:：…]{1,2})\s*/;
    sentences = text.split(sentencePattern);
  }

  const chunks = [];
  let currentChunk = "";

  for (const sentence of sentences) {
    // 如果单个句子就超过了最大长度，需要进一步分割
    if (sentence.length > maxLength) {
      if (currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = "";
      }
      // 使用智能分割替代原来的硬切分
      const subChunks = smartSplitLongSentence(sentence, maxLength);
      chunks.push(...subChunks);
    }
    // 如果当前块加上新句子会超过最大长度
    else if (currentChunk.length + sentence.length > maxLength) {
      chunks.push(currentChunk.trim());
      currentChunk = sentence;
    } else {
      currentChunk += sentence;
    }
  }

  if (currentChunk) {
    chunks.push(currentChunk.trim());
  }

  return chunks;
}

/**
 * 【新增】智能分割超长文本的通用函数
 * 用于处理SSML和传统文本的超长片段分割
 * 保持原有的所有智能分割逻辑和性能优化
 */
function smartSplitLongText(text, maxLength) {
  const subChunks = [];
  let remainingText = text;

  while (remainingText.length > maxLength) {
    let splitPos = -1;
    const searchRange = remainingText.substring(0, maxLength);

    // 优化：使用单次遍历查找最佳分割点，按优先级从高到低
    // 1. 次要标点符号（逗号、分号、冒号等） - 使用lastIndexOf一次查找
    const punctuationChars = '，,;；:：';
    for (let i = 0; i < punctuationChars.length; i++) {
      const pos = searchRange.lastIndexOf(punctuationChars[i]);
      if (pos > splitPos) {
        splitPos = pos + 1; // 在标点后分割
      }
    }

    // 2. 如果没找到次要标点，寻找空格（主要用于英文）
    if (splitPos === -1) {
      splitPos = searchRange.lastIndexOf(' ');
    }

    // 3. 寻找连接符 - 优化：直接使用lastIndexOf
    if (splitPos === -1) {
      const connectorChars = '-–—';
      for (let i = 0; i < connectorChars.length; i++) {
        const pos = searchRange.lastIndexOf(connectorChars[i]);
        if (pos > splitPos) {
          splitPos = pos + 1;
        }
      }
    }

    // 4. 寻找换行符或制表符
    if (splitPos === -1) {
      splitPos = Math.max(
        searchRange.lastIndexOf('\n'),
        searchRange.lastIndexOf('\r'),
        searchRange.lastIndexOf('\t')
      );
    }

    // 5. 如果都找不到合适的分割点，使用硬切分，但尽量避免切断单词
    if (splitPos === -1 || splitPos < maxLength * 0.7) {
      splitPos = maxLength;

      // 优化：简化英文单词边界检测
      if (splitPos < remainingText.length) {
        const charAtSplit = remainingText[splitPos];
        const charBeforeSplit = remainingText[splitPos - 1];

        // 如果切分点在英文字母中间，向前寻找单词边界
        if (/[a-zA-Z]/.test(charAtSplit) && /[a-zA-Z]/.test(charBeforeSplit)) {
          // 优化：使用lastIndexOf查找空格而不是while循环
          const wordBoundary = remainingText.lastIndexOf(' ', splitPos);
          if (wordBoundary > maxLength * 0.8) {
            splitPos = wordBoundary;
          }
        }
      }
    }

    // 确保分割位置有效
    splitPos = Math.max(1, Math.min(splitPos, remainingText.length));

    const chunk = remainingText.substring(0, splitPos).trim();
    if (chunk) {
      subChunks.push(chunk);
    }

    remainingText = remainingText.substring(splitPos).trim();
  }

  // 添加剩余部分
  if (remainingText.trim()) {
    subChunks.push(remainingText.trim());
  }

  return subChunks;
}

/**
 * 【保持原有】智能分割超长句子函数 - 用于传统文本处理
 * 这是原有的smartSplitLongSentence函数，保持100%兼容性
 */
function smartSplitLongSentence(sentence, maxLength) {
  // 直接复用通用的智能分割函数
  return smartSplitLongText(sentence, maxLength);
}

// ========== TTS 代理健康检查函数 ==========

/**
 * 【新增】获取代理健康检查URL
 * @param {string} proxyUrl - 代理URL
 * @returns {string} 健康检查URL
 */
function getProxyHealthUrl(proxyUrl) {
  if (proxyUrl.includes('amazonaws.com')) {
    return proxyUrl + '/api/v1/health';  // AWS Lambda代理
  } else {
    return proxyUrl + '/api/health';     // 其他所有代理
  }
}

/**
 * 【新增】检查单个代理的健康状态
 * @param {string} proxyUrl - 代理URL
 * @param {object} proxyConfig - 代理配置
 * @param {object} env - 环境变量
 * @returns {Promise<boolean>} 代理是否健康
 */
async function checkSingleProxyHealth(proxyUrl, proxyConfig, env) {
  const healthUrl = getProxyHealthUrl(proxyUrl);
  const maxAttempts = proxyConfig.TTS_HEALTH_CHECK_RETRIES;
  const interval = proxyConfig.TTS_HEALTH_CHECK_INTERVAL;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      if (proxyConfig.ENABLE_PROXY_DEBUG) {
        console.log(`[HEALTH-CHECK] 🏥 Checking proxy health (attempt ${attempt}/${maxAttempts}): ${proxyUrl}`);
      }

      const response = await fetch(healthUrl, {
        method: 'GET',
        headers: {
          'x-proxy-secret': proxyConfig.TTS_PROXY_SECRET
        },
        signal: AbortSignal.timeout(proxyConfig.TTS_HEALTH_CHECK_TIMEOUT)
      });

      if (response.status === 200) {
        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.log(`[HEALTH-CHECK] ✅ Proxy healthy: ${proxyUrl}`);
        }
        return true;
      }

      if (proxyConfig.ENABLE_PROXY_DEBUG) {
        console.warn(`[HEALTH-CHECK] ⚠️ Proxy health check failed (status ${response.status}): ${proxyUrl}`);
      }
    } catch (error) {
      if (proxyConfig.ENABLE_PROXY_DEBUG) {
        console.warn(`[HEALTH-CHECK] ❌ Proxy health check error (attempt ${attempt}/${maxAttempts}): ${proxyUrl} - ${error.message}`);
      }
    }

    // 如果不是最后一次尝试，等待后重试
    if (attempt < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, interval));
    }
  }

  if (proxyConfig.ENABLE_PROXY_DEBUG) {
    console.warn(`[HEALTH-CHECK] 💀 Proxy marked as unhealthy after ${maxAttempts} attempts: ${proxyUrl}`);
  }
  return false;
}

/**
 * 【新增】快速检查单个代理的实时健康状态（用于实际请求前验证）
 * @param {string} proxyUrl - 代理URL
 * @param {object} proxyConfig - 代理配置
 * @param {object} env - 环境变量
 * @param {number} timeoutMs - 快速检查超时时间（默认3秒）
 * @returns {Promise<boolean>} 代理是否健康
 */
async function quickHealthCheck(proxyUrl, proxyConfig, env, timeoutMs = 3000) {
  const healthUrl = getProxyHealthUrl(proxyUrl);

  try {
    if (proxyConfig.ENABLE_PROXY_DEBUG) {
      console.log(`[QUICK-HEALTH] 🚀 Quick health check for proxy: ${proxyUrl} (${timeoutMs}ms timeout)`);
    }

    const response = await fetch(healthUrl, {
      method: 'GET',
      headers: {
        'x-proxy-secret': proxyConfig.TTS_PROXY_SECRET
      },
      signal: AbortSignal.timeout(timeoutMs)
    });

    const isHealthy = response.status === 200;

    if (proxyConfig.ENABLE_PROXY_DEBUG) {
      if (isHealthy) {
        console.log(`[QUICK-HEALTH] ✅ Proxy quick check passed: ${proxyUrl}`);
      } else {
        console.warn(`[QUICK-HEALTH] ⚠️ Proxy quick check failed (status ${response.status}): ${proxyUrl}`);
      }
    }

    return isHealthy;
  } catch (error) {
    if (proxyConfig.ENABLE_PROXY_DEBUG) {
      console.warn(`[QUICK-HEALTH] ❌ Proxy quick check error: ${proxyUrl} - ${error.message}`);
    }
    return false;
  }
}

/**
 * 【新增】批量检查代理健康状态并返回健康的代理列表
 * @param {string[]} proxyUrls - 代理URL列表
 * @param {object} proxyConfig - 代理配置
 * @param {object} env - 环境变量
 * @returns {Promise<string[]>} 健康的代理URL列表
 */
async function getHealthyProxies(proxyUrls, proxyConfig, env) {
  if (!proxyConfig.TTS_HEALTH_CHECK_ENABLED || !proxyUrls || proxyUrls.length === 0) {
    // 健康检查被禁用或没有代理，返回原始列表
    return proxyUrls || [];
  }

  const healthCheckStartTime = Date.now();

  if (proxyConfig.ENABLE_PROXY_DEBUG) {
    console.log(`[HEALTH-CHECK] 🏥 Starting health check for ${proxyUrls.length} proxies...`);
  }

  // 并行检查所有代理的健康状态
  const healthResults = await Promise.all(
    proxyUrls.map(async (proxyUrl, index) => ({
      url: proxyUrl,
      index: index,
      healthy: await checkSingleProxyHealth(proxyUrl, proxyConfig, env)
    }))
  );

  // 筛选出健康的代理
  const healthyProxies = healthResults
    .filter(result => result.healthy)
    .map(result => result.url);

  const healthCheckDuration = Date.now() - healthCheckStartTime;

  if (proxyConfig.ENABLE_PROXY_DEBUG) {
    console.log(`[HEALTH-CHECK] 📊 Health check completed in ${healthCheckDuration}ms:`, {
      totalProxies: proxyUrls.length,
      healthyProxies: healthyProxies.length,
      unhealthyProxies: proxyUrls.length - healthyProxies.length,
      healthyUrls: healthyProxies,
      duration: healthCheckDuration
    });
  }

  // 记录健康检查结果到日志
  if (env._log) {
    env._log.info('Proxy health check completed', {
      totalProxies: proxyUrls.length,
      healthyProxies: healthyProxies.length,
      unhealthyProxies: proxyUrls.length - healthyProxies.length,
      duration: healthCheckDuration,
      healthyUrls: healthyProxies
    });
  }

  return healthyProxies;
}

// ========== TTS 代理相关函数 ==========

/**
 * 【升级版】判断是否应该尝试代理（支持多代理检查）
 * @param {Error} error - 错误对象
 * @param {object} proxyConfig - 代理配置
 * @returns {boolean} 是否应该尝试代理
 */
function shouldAttemptProxy(error, proxyConfig) {
  // 检查代理是否可用 - 支持新旧两种配置方式
  if (!proxyConfig.ENABLE_TTS_PROXY || !proxyConfig.TTS_PROXY_SECRET) {
    return false;
  }

  // 检查是否有可用的代理URL（新版多URL或旧版单URL）
  const hasProxyUrls = proxyConfig.TTS_PROXY_URLS && proxyConfig.TTS_PROXY_URLS.length > 0;
  const hasLegacyUrl = proxyConfig.TTS_PROXY_URL;

  if (!hasProxyUrls && !hasLegacyUrl) {
    return false;
  }

  // 1. 数据中心级别的错误（原有逻辑）
  if (error.isDataCenterRetryable) {
    return true;
  }

  // 2. 服务器错误（5xx）
  if (error.status >= 500) {
    return true;
  }

  // 3. 超时错误
  if (error.name === 'AbortError' || error.message.includes('timeout')) {
    return true;
  }

  // 4. 网络连接错误
  if (error.message.includes('fetch') || error.message.includes('network')) {
    return true;
  }

  // 5. 特定的 4xx 错误（如 429 Too Many Requests）
  if (error.status === 429) {
    return true;
  }

  // 6. 其他可重试的错误关键词
  const errorMessage = error.message?.toLowerCase() || '';
  const retryableKeywords = [
    'connection',
    'refused',
    'reset',
    'unavailable',
    'overloaded'
  ];

  return retryableKeywords.some(keyword => errorMessage.includes(keyword));
}

/**
 * 【升级版】调用TTS代理，支持集群级故障的退避重试
 * 在原有多代理故障转移基础上，增加集群级重试、指数退避和抖动机制
 * @param {string[]} proxyUrls - 代理服务器URL列表
 * @param {string} voiceId - 语音ID
 * @param {object} payload - 请求负载
 * @param {object} proxyConfig - 完整的代理配置对象
 * @param {object} env - 环境变量
 * @returns {Promise<ArrayBuffer>} 音频数据
 */
async function callTtsProxyWithFailover(proxyUrls, voiceId, payload, proxyConfig, env, context = {}, signal = null) {
  // 检查是否有可用的代理URL
  if (!proxyUrls || proxyUrls.length === 0) {
    throw new Error('No proxy URLs configured or available.');
  }

  // 【修改】移除批量健康检查预筛选，改用实时健康验证
  // 直接使用原始代理列表，在实际请求前进行快速健康检查
  let urlsToUse = proxyUrls;

  if (proxyConfig.TTS_HEALTH_CHECK_ENABLED && proxyConfig.ENABLE_PROXY_DEBUG) {
    console.log(`[HEALTH-CHECK] 🚀 Using real-time health verification for ${proxyUrls.length} proxies (no pre-screening)`);
  } else if (proxyConfig.ENABLE_PROXY_DEBUG) {
    console.log(`[HEALTH-CHECK] 🚫 Health check disabled, using all ${proxyUrls.length} proxies`);
  }

  // --- START: 新增的集群级重试逻辑 ---
  const clusterRetryAttempts = proxyConfig.TTS_CLUSTER_RETRY_COUNT; // 使用配置的集群重试次数
  let lastClusterError = null;

  for (let attempt = 1; attempt <= clusterRetryAttempts; attempt++) {
    if (proxyConfig.ENABLE_PROXY_DEBUG && attempt > 1) {
      console.log(`[PROXY-CLUSTER-RETRY] Starting attempt #${attempt}/${clusterRetryAttempts} for the entire proxy cluster.`);
    }

    // 【关键修复】将变量移到 try 块外，确保在 catch 块中可访问
    const failoverStartTime = Date.now();
    let urlsToTry = urlsToUse; // 【修改】使用健康检查后的代理列表

    // --- START: 【新增】代理选择策略逻辑 ---
    // 如果策略是 'random'，则打乱数组顺序
    if (proxyConfig.TTS_PROXY_SELECTION_STRATEGY === 'random') {
      // 创建一个副本进行操作，避免修改原始传入的数组 (好习惯)
      const shuffledUrls = [...urlsToUse]; // 【修改】使用健康检查后的代理列表

      // 使用 Fisher-Yates (aka Knuth) 洗牌算法，这是最高效且无偏的洗牌算法
      for (let i = shuffledUrls.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffledUrls[i], shuffledUrls[j]] = [shuffledUrls[j], shuffledUrls[i]];
      }

      urlsToTry = shuffledUrls;

      if (proxyConfig.ENABLE_PROXY_DEBUG) {
        console.log('[PROXY-STRATEGY] Random selection strategy enabled. Shuffled healthy proxy order:', urlsToTry);
      }
    }
    // --- END: 代理选择策略逻辑 ---

    try {

      // --- START: 增强的多代理故障转移逻辑 ---

      // 【增强日志】使用统一日志系统记录故障转移开始
      if (env._log && attempt === 1) {
        env._log.flow('proxy-failover-start', 'initiated', {
          proxyCount: urlsToTry.length,
          strategy: proxyConfig.TTS_PROXY_SELECTION_STRATEGY,
          voiceId: voiceId,
          textLength: payload.text?.length,
          clusterAttempt: attempt,
          maxClusterAttempts: clusterRetryAttempts
        });
      } else if (proxyConfig.ENABLE_PROXY_DEBUG && attempt === 1) {

      // 【新增】通知进度更新 - 代理故障转移开始
      if (context.updateTaskStep && attempt === 1) {
        context.updateTaskStep('proxy_failover');
      }
        console.log(`[PROXY-FAILOVER] 🚀 Starting multi-proxy failover with ${urlsToTry.length} proxy(ies):`, {
          proxyCount: urlsToTry.length,
          proxyUrls: urlsToTry,
          originalOrder: proxyUrls,
          strategy: proxyConfig.TTS_PROXY_SELECTION_STRATEGY,
          voiceId: voiceId,
          textLength: payload.text?.length,
          timestamp: new Date().toISOString()
        });
      }

      // 遍历所有配置的代理URL，实现故障转移（顺序或随机）
      for (let i = 0; i < urlsToTry.length; i++) {
        const proxyUrl = urlsToTry[i];
        const fullUrl = `${proxyUrl}/api/v1/text-to-speech/${voiceId}`;
        const proxyStartTime = Date.now();

        // 【增强日志】记录每个代理尝试
        if (env._log) {
          env._log.flow('proxy-attempt', 'starting', {
            proxyIndex: i + 1,
            totalProxies: urlsToTry.length,
            proxyUrl: proxyUrl,
            clusterAttempt: attempt
          });
        } else if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.log(`[PROXY-FAILOVER] 🔄 Attempting proxy #${i + 1}/${urlsToTry.length} (Cluster attempt #${attempt}): ${proxyUrl}`);
        }

        try {
          // 【新增】实时健康验证：在实际请求前进行快速健康检查
          if (proxyConfig.TTS_HEALTH_CHECK_ENABLED) {
            const quickCheckStart = Date.now();
            const isStillHealthy = await quickHealthCheck(proxyUrl, proxyConfig, env, proxyConfig.TTS_HEALTH_CHECK_TIMEOUT); // 使用配置的快速检查超时
            const quickCheckDuration = Date.now() - quickCheckStart;

            if (!isStillHealthy) {
              if (proxyConfig.ENABLE_PROXY_DEBUG) {
                console.warn(`[QUICK-HEALTH] ❌ Proxy #${i + 1} failed quick health check (${quickCheckDuration}ms), skipping to next proxy: ${proxyUrl}`);
              }

              // 立即跳过此代理，不等待长时间超时
              if (i === urlsToTry.length - 1) {
                throw new Error(`All proxies failed quick health check on cluster attempt #${attempt}`);
              }
              continue; // 跳到下一个代理
            }

            if (proxyConfig.ENABLE_PROXY_DEBUG) {
              console.log(`[QUICK-HEALTH] ✅ Proxy #${i + 1} passed quick health check (${quickCheckDuration}ms), proceeding with request: ${proxyUrl}`);
            }
          }

          // 【修改】智能超时：通过快速健康检查的代理使用60秒，其他使用45秒
          const timeoutMs = proxyConfig.TTS_HEALTH_CHECK_ENABLED ?
            proxyConfig.TTS_HEALTHY_PROXY_TIMEOUT :
            proxyConfig.TTS_PROXY_TIMEOUT;

          // 【新增】组合超时信号和快速失败信号
          let combinedSignal;
          if (signal) {
            combinedSignal = AbortSignal.any([signal, AbortSignal.timeout(timeoutMs)]);
          } else {
            combinedSignal = AbortSignal.timeout(timeoutMs);
          }

          if (proxyConfig.ENABLE_PROXY_DEBUG) {
            console.log(`[PROXY-TIMEOUT] Using ${timeoutMs/1000}s timeout for proxy #${i + 1} (health check ${proxyConfig.TTS_HEALTH_CHECK_ENABLED ? 'enabled' : 'disabled'})`);
          }

          // 对单个代理进行调用，包含重试逻辑
          const response = await fetch(fullUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-proxy-secret': proxyConfig.TTS_PROXY_SECRET
            },
            body: JSON.stringify(payload),
            signal: combinedSignal // 【修改】使用组合信号
          });

          if (response.ok) {
            const audioBuffer = await response.arrayBuffer();

            if (proxyConfig.ENABLE_PROXY_DEBUG) {
              console.log(`[PROXY-SUCCESS] ✅ Proxy #${i + 1} (${proxyUrl}) successful on cluster attempt #${attempt}!`, {
                audioSize: audioBuffer.byteLength,
                audioSizeKB: (audioBuffer.byteLength / 1024).toFixed(2) + ' KB'
              });
            }

            // 【记录成功】传递完整上下文信息
            const successContext = {
              ...context,
              proxyUrl,
              voiceId,
              responseStatus: response.status
            };
            await recordProxySuccess(env, proxyConfig, successContext);
            return audioBuffer; // 只要有一个成功，就立即返回
          } else {
            // HTTP状态码表示失败，记录并继续尝试下一个
            let errorDataText = await response.text().catch(() => '');
            let errorData, originalMessage;

            try {
              // 尝试解析为JSON以获取详细错误信息
              errorData = JSON.parse(errorDataText);
              originalMessage = errorData?.detail?.message || errorData?.message || errorDataText;
            } catch (e) {
              // 如果解析失败，使用原始文本
              originalMessage = errorDataText;
              errorData = { message: originalMessage };
            }

            // 【新增】检测违规并立即终止所有代理尝试
            if (isContentViolationError(response.status, errorData, originalMessage)) {
              const violationError = new Error(originalMessage);
              violationError.status = response.status;
              violationError.isContentViolation = true;
              violationError.isDataCenterRetryable = false;

              if (proxyConfig.ENABLE_PROXY_DEBUG) {
                console.warn(`[PROXY-FAILOVER] Content violation from Proxy #${i + 1}. Terminating failover.`);
              }

              throw violationError; // 立即抛出，不尝试其他代理
            }

            const errorMessage = `Proxy #${i + 1} failed with status ${response.status}`;
            const proxyDuration = Date.now() - proxyStartTime;

            // 【增强日志】使用统一日志系统记录HTTP失败
            if (env._log) {
              env._log.error(new Error(errorMessage), {
                proxyIndex: i + 1,
                totalProxies: urlsToTry.length,
                proxyUrl: proxyUrl,
                responseStatus: response.status,
                duration: proxyDuration,
                errorDetails: errorDataText.substring(0, 200),
                isLastProxy: i === urlsToTry.length - 1,
                willRetryCluster: i === urlsToTry.length - 1 && attempt < clusterRetryAttempts
              });
            } else if (proxyConfig.ENABLE_PROXY_DEBUG) {
              console.warn(`[PROXY-FAILOVER] ❌ ${errorMessage}. Error: ${errorDataText.substring(0, 200)}`);
            }

            // 【记录HTTP失败】立即记录每个代理的失败
            const httpFailureError = new Error(errorMessage);
            httpFailureError.status = response.status;
            httpFailureError.isProxyError = true;
            const httpFailureContext = {
              ...context,
              error: httpFailureError,
              proxyUrl,
              voiceId,
              responseStatus: response.status,
              duration: proxyDuration
            };
            await recordProxyEvent(false, env, proxyConfig, httpFailureContext);

            // 如果这是最后一个代理，抛出错误触发集群重试；否则继续尝试下一个
            if (i === urlsToTry.length - 1) {
              throw httpFailureError;
            }
          }
        } catch (error) {
          // 【新增】检测违规错误并立即传播
          if (error.isContentViolation) {
            throw error; // 违规错误立即向上传播
          }

          // 网络层面的错误 (如DNS解析失败, 连接被拒, 超时)
          const proxyDuration = Date.now() - proxyStartTime;

          // 【增强日志】使用统一日志系统记录网络错误
          if (env._log) {
            env._log.error(error, {
              proxyIndex: i + 1,
              totalProxies: urlsToTry.length,
              proxyUrl: proxyUrl,
              errorType: 'network',
              duration: proxyDuration,
              isLastProxy: i === urlsToTry.length - 1,
              willRetryCluster: i === urlsToTry.length - 1 && attempt < clusterRetryAttempts
            });
          } else if (proxyConfig.ENABLE_PROXY_DEBUG) {
            console.warn(`[PROXY-FAILOVER] ❌ Proxy #${i + 1} (${proxyUrl}) failed with network error: ${error.message}`);
          }

          // 【记录网络失败】立即记录每个代理的网络失败
          const networkFailureContext = {
            ...context,
            error,
            proxyUrl,
            voiceId,
            responseStatus: error.status || 0,
            duration: proxyDuration
          };
          await recordProxyEvent(false, env, proxyConfig, networkFailureContext);

          // 如果这是最后一个代理，抛出错误触发集群重试；否则继续尝试下一个
          if (i === urlsToTry.length - 1) {
            throw error;
          }
        }
      }
      // --- END: 原有的多代理故障转移逻辑 ---

      // 如果代码能执行到这里，说明内部循环已经尝试了所有代理，但都失败了。
      // 抛出一个错误，以便被外层的 try-catch 捕获并触发集群重试。
      throw new Error('All proxies in the cluster failed in this attempt.');

    } catch (error) {
      lastClusterError = error; // 保存最近一次的错误信息
      const failoverDuration = Date.now() - failoverStartTime;

      // 如果这是最后一次集群重试，就彻底失败
      if (attempt === clusterRetryAttempts) {
        // 【增强日志】使用统一日志系统记录最终失败
        if (env._log) {
          env._log.error(lastClusterError, {
            operation: 'proxy-cluster-retry-final-failure',
            totalAttempts: clusterRetryAttempts,
            totalDuration: failoverDuration,
            proxyCount: urlsToTry.length,
            strategy: proxyConfig.TTS_PROXY_SELECTION_STRATEGY
          });
        } else {
          console.error(`[PROXY-CLUSTER-RETRY] All ${clusterRetryAttempts} cluster retry attempts failed. Giving up.`);
        }

        await recordProxyFailure(lastClusterError, env, proxyConfig);

        // 【关键修复】确保代理故障转移失败的错误有正确的标志
        if (!lastClusterError.isProxyFailoverFailure) {
          lastClusterError.isProxyFailoverFailure = true;
        }

        // 【增强日志】记录最终的代理故障转移失败
        if (env._log) {
          env._log.error(lastClusterError, {
            operation: 'proxy-failover-final-failure',
            totalAttempts: clusterRetryAttempts,
            totalDuration: Date.now() - failoverStartTime,
            proxyCount: urlsToTry.length,
            strategy: proxyConfig.TTS_PROXY_SELECTION_STRATEGY,
            voiceId: voiceId,
            textLength: payload.text?.length
          });
        }

        throw lastClusterError; // 抛出最终的错误
      }

      // --- START: 指数退避与抖动 ---
      if (proxyConfig.TTS_ENABLE_BACKOFF) {
        const baseDelay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s...
        const jitter = Math.random() * 500; // 0-500ms的抖动
        const totalDelay = Math.min(baseDelay + jitter, proxyConfig.TTS_CLUSTER_MAX_DELAY); // 使用配置的最大延迟

        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.warn(`[PROXY-CLUSTER-RETRY] Cluster attempt #${attempt} failed. Waiting for ${totalDelay.toFixed(0)}ms before next retry.`);
        }

        await new Promise(resolve => setTimeout(resolve, totalDelay));
      } else {
        // 如果禁用了退避，使用固定的短延迟
        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.warn(`[PROXY-CLUSTER-RETRY] Cluster attempt #${attempt} failed. Waiting for 1000ms before next retry (backoff disabled).`);
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      // --- END: 指数退避与抖动 ---
    }
  }
  // --- END: 新增的集群级重试逻辑 ---
}

/**
 * 【保留】调用 Vercel 代理进行 TTS（向后兼容版本）
 * @param {string} voiceId - 语音ID
 * @param {object} payload - 请求负载
 * @param {object} proxyConfig - 代理配置
 * @param {object} env - 环境变量
 * @returns {Promise<ArrayBuffer>} 音频数据
 */
async function callVercelProxyFallback(voiceId, payload, proxyConfig, env, context = {}, signal = null) {
  // 检查配置完整性
  if (!proxyConfig.TTS_PROXY_URL || !proxyConfig.TTS_PROXY_SECRET) {
    throw new Error('Proxy configuration incomplete');
  }

  const proxyUrl = `${proxyConfig.TTS_PROXY_URL}/api/v1/text-to-speech/${voiceId}`;

  if (proxyConfig.ENABLE_PROXY_DEBUG) {
    console.log(`[PROXY-FALLBACK] 🔄 Attempting Vercel proxy fallback:`, {
      url: proxyUrl,
      textLength: payload.text?.length,
      modelId: payload.model_id,
      timestamp: new Date().toISOString()
    });
  }

  let retries = proxyConfig.TTS_PROXY_RETRY_COUNT;
  let lastError = null;

  while (retries > 0) {
    try {
      if (proxyConfig.ENABLE_PROXY_DEBUG) {
        console.log(`[PROXY-FALLBACK] 📤 Proxy attempt ${proxyConfig.TTS_PROXY_RETRY_COUNT - retries + 1}/${proxyConfig.TTS_PROXY_RETRY_COUNT}`);
      }

      // 【新增】智能超时：健康代理使用60秒，其他使用原配置
      const timeoutMs = proxyConfig.TTS_HEALTH_CHECK_ENABLED ?
        proxyConfig.TTS_HEALTHY_PROXY_TIMEOUT :
        proxyConfig.TTS_PROXY_TIMEOUT;

      // 【新增】组合超时信号和快速失败信号
      let combinedSignal;
      if (signal) {
        combinedSignal = AbortSignal.any([signal, AbortSignal.timeout(timeoutMs)]);
      } else {
        combinedSignal = AbortSignal.timeout(timeoutMs);
      }

      if (proxyConfig.ENABLE_PROXY_DEBUG) {
        console.log(`[PROXY-FALLBACK] Using ${timeoutMs/1000}s timeout (health check ${proxyConfig.TTS_HEALTH_CHECK_ENABLED ? 'enabled' : 'disabled'})`);
      }

      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-proxy-secret': proxyConfig.TTS_PROXY_SECRET
        },
        body: JSON.stringify(payload),
        signal: combinedSignal // 【修改】使用组合信号替换timeout
      });

      if (response.ok) {
        const audioBuffer = await response.arrayBuffer();

        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.log(`[PROXY-FALLBACK] ✅ Proxy request successful:`, {
            status: response.status,
            audioSize: audioBuffer.byteLength + ' bytes',
            audioSizeKB: (audioBuffer.byteLength / 1024).toFixed(2) + ' KB',
            timestamp: new Date().toISOString()
          });
        }

        // 【记录代理成功】传递完整上下文信息
        const successContext = {
          ...context,
          proxyUrl: proxyConfig.TTS_PROXY_URL,
          voiceId,
          responseStatus: response.status
        };
        await recordProxySuccess(env, proxyConfig, successContext);

        return audioBuffer;
      } else {
        let errorDataText = await response.text().catch(() => '');
        let errorData, originalMessage;

        try {
          // 尝试解析为JSON以获取详细错误信息
          errorData = JSON.parse(errorDataText);
          originalMessage = errorData?.detail?.message || errorData?.error || errorData?.message || errorDataText;
        } catch (e) {
          // 如果解析失败，使用原始文本
          originalMessage = errorDataText;
          errorData = { message: originalMessage };
        }

        // 【新增】检测违规并立即终止重试循环
        if (isContentViolationError(response.status, errorData, originalMessage)) {
          const violationError = new Error(originalMessage);
          violationError.status = response.status;
          violationError.isContentViolation = true;
          violationError.isDataCenterRetryable = false;

          if (proxyConfig.ENABLE_PROXY_DEBUG) {
            console.error(`[PROXY-FALLBACK] Content violation detected. Terminating retries.`);
          }

          throw violationError; // 立即抛出，不进行重试
        }

        const errorMessage = originalMessage || `Proxy failed with status ${response.status}`;

        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.error(`[PROXY-FALLBACK] ❌ Proxy request failed:`, {
            status: response.status,
            error: errorMessage,
            attempt: proxyConfig.TTS_PROXY_RETRY_COUNT - retries + 1,
            willRetry: retries > 1
          });
        }

        lastError = new Error(errorMessage);
        lastError.status = response.status;
        lastError.isProxyError = true;

        throw lastError;
      }
    } catch (error) {
      // 【新增】检测违规错误并立即终止重试
      if (error.isContentViolation) {
        throw error; // 违规错误立即终止重试
      }

      retries--;
      lastError = error;

      const currentAttempt = proxyConfig.TTS_PROXY_RETRY_COUNT - retries;

      if (proxyConfig.ENABLE_PROXY_DEBUG) {
        console.warn(`[PROXY-FALLBACK] ⚠️ Proxy error, ${retries > 0 ? 'retrying' : 'giving up'}:`, {
          error: error.message,
          attempt: currentAttempt,
          remainingRetries: retries,
          timestamp: new Date().toISOString()
        });
      }

      if (retries === 0) {
        // 【记录代理失败】传递完整上下文信息
        const failureContext = {
          ...context,
          error,
          proxyUrl: proxyConfig.TTS_PROXY_URL,
          voiceId,
          responseStatus: error.status || 0
        };
        await recordProxyFailure(error, env, proxyConfig, failureContext);
        throw error;
      }

      // --- START: 指数退避与抖动（单代理重试） ---
      if (proxyConfig.TTS_ENABLE_BACKOFF) {
        const baseDelay = Math.pow(2, currentAttempt - 1) * 1000; // 1s, 2s, 4s...
        const jitter = Math.random() * 300; // 0-300ms的抖动（比集群重试稍小）
        const totalDelay = Math.min(baseDelay + jitter, proxyConfig.TTS_SINGLE_MAX_DELAY); // 使用配置的最大延迟

        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.warn(`[PROXY-FALLBACK] Waiting for ${totalDelay.toFixed(0)}ms before retry attempt ${currentAttempt + 1}.`);
        }

        await new Promise(resolve => setTimeout(resolve, totalDelay));
      } else {
        // 如果禁用了退避，使用原有的固定延迟
        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.warn(`[PROXY-FALLBACK] Waiting for 1500ms before retry attempt ${currentAttempt + 1} (backoff disabled).`);
        }
        await new Promise(resolve => setTimeout(resolve, 1500));
      }
      // --- END: 指数退避与抖动 ---
    }
  }
}

/**
 * 【保留】记录代理成功统计 - 向后兼容包装函数
 */
async function recordProxySuccess(env, proxyConfig, context = {}) {
  await recordProxyEvent(true, env, proxyConfig, context);
}

/**
 * 【升级版】统一的代理事件记录函数 - 支持Analytics Engine详细分析
 * @param {boolean} isSuccess - 是否成功
 * @param {object} env - 环境变量
 * @param {object} proxyConfig - 代理配置
 * @param {object} context - 上下文信息 {error, proxyUrl, voiceId, taskId, username, responseStatus}
 */
async function recordProxyEvent(isSuccess, env, proxyConfig, context = {}) {
  if (!proxyConfig.ENABLE_PROXY_STATS) return;

  const { error, proxyUrl, voiceId, taskId, username, responseStatus } = context;

  try {
    // 1. 保留原有的KV计数器逻辑（用于快速概览和向后兼容）
    const eventType = isSuccess ? 'success' : 'failure';
    const key = `proxy_stats:${eventType}:${getDateKey()}`;
    const current = await env.TTS_STATUS.get(key) || '0';
    await env.TTS_STATUS.put(key, (parseInt(current) + 1).toString(), {
      expirationTtl: 86400 // 24小时
    });

    // 2. 如果是失败，记录最近失败次数（用于触发预防性代理）
    if (!isSuccess) {
      const recentKey = `proxy_recent_failures`;
      const recentFailures = await env.TTS_STATUS.get(recentKey) || '0';
      await env.TTS_STATUS.put(recentKey, (parseInt(recentFailures) + 1).toString(), {
        expirationTtl: proxyConfig.TTS_FALLBACK_WINDOW // 使用配置的时间窗口
      });
    }

    // 3. 【新增】Analytics Engine 详细事件记录
    if (env.PROXY_ANALYTICS) {
      try {
        const blobs = [
          isSuccess ? 'success' : 'failure',                    // blob 0: event_type
          proxyUrl || 'N/A',                                    // blob 1: proxy_url
          taskId || 'N/A',                                      // blob 2: task_id
          username || 'N/A',                                    // blob 3: username
          voiceId || 'N/A',                                     // blob 4: voice_id
          isSuccess ? 'OK' : (error?.message || 'Unknown error').substring(0, 100), // blob 5: message
        ];

        const doubles = [
          isSuccess ? 1 : 0,                                    // double 0: success_count
          isSuccess ? 0 : 1,                                    // double 1: failure_count
          responseStatus || (error?.status || 0),               // double 2: status_code
        ];

        // 【关键修改】只使用一个索引，符合Cloudflare免费计划限制
        const indexes = [
          proxyUrl || 'N/A'                                     // index 0: proxyUrl (用于过滤)
        ];

        env.PROXY_ANALYTICS.writeDataPoint({ blobs, doubles, indexes });

        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.log(`[ANALYTICS-PROXY] 📊 Recorded ${eventType} event:`, {
            proxyUrl: proxyUrl || 'N/A',
            taskId: taskId || 'N/A',
            username: username || 'N/A',
            voiceId: voiceId || 'N/A',
            status: responseStatus || (error?.status || 0)
          });
        }
      } catch (analyticsError) {
        console.warn('[ANALYTICS-PROXY] Failed to write data point:', analyticsError.message);
      }
    }

    if (proxyConfig.ENABLE_PROXY_DEBUG) {
      const logMessage = isSuccess ?
        `[PROXY-STATS] ✅ Recorded proxy success` :
        `[PROXY-STATS] ❌ Recorded proxy failure`;

      console.log(logMessage, {
        proxyUrl: proxyUrl || 'N/A',
        error: error?.message || 'N/A',
        taskId: taskId || 'N/A'
      });
    }
  } catch (statsError) {
    console.warn('[PROXY-STATS] Failed to record event:', statsError.message);
  }
}

/**
 * 【保留】记录代理失败统计 - 向后兼容包装函数
 */
async function recordProxyFailure(error, env, proxyConfig, context = {}) {
  await recordProxyEvent(false, env, proxyConfig, { ...context, error });
}

/**
 * 【新增】获取日期键（用于统计）
 */
function getDateKey() {
  return new Date().toISOString().split('T')[0]; // YYYY-MM-DD
}

async function generateSpeech(text, voiceId, modelId, stability, similarity_boost, style, speed, env, context = {}, signal = null) {
  // 【新增】获取代理配置，检查是否启用仅代理模式
  const proxyConfig = getTTSProxyConfig(env);

  // 根据不同模型构建相应的 voice_settings
  let voice_settings = {};

  if (modelId === 'eleven_v3') {
    // Eleven v3 模型只支持 stability 参数
    voice_settings = {
      stability: stability || 0.5,  // eleven_v3 默认使用 0.5
      use_speaker_boost: true       // 启用Speaker Boost增强音质
    };
  } else if (modelId === 'eleven_turbo_v2' || modelId === 'eleven_turbo_v2_5') {
    // Eleven Turbo v2/v2.5 模型不支持 style 参数
    voice_settings = {
      stability: stability || 0.58,
      similarity_boost: similarity_boost || 0.75,
      speed: speed || 1.00,
      use_speaker_boost: true       // 启用Speaker Boost增强音质
    };
  } else {
    // 其他模型支持完整参数
    voice_settings = {
      stability: stability || 0.58,
      similarity_boost: similarity_boost || 0.75,
      style: style || 0.50,
      speed: speed || 1.00,
      use_speaker_boost: true       // 启用Speaker Boost增强音质
    };
  }

  const payload = {
    text: text,
    model_id: modelId,
    voice_settings: voice_settings
  };

  // 【新增】检查是否启用仅代理模式
  if (proxyConfig.TTS_PROXY_MODE === 'proxy' || proxyConfig.TTS_PROXY_MODE === 'proxy_only') {
    // --- 路径A: 仅代理模式 ---
    if (env._log) {
      env._log.info(`Proxy-only mode enabled (${proxyConfig.TTS_PROXY_MODE}). Bypassing direct ElevenLabs call.`, {
        mode: proxyConfig.TTS_PROXY_MODE,
        availableProxies: proxyConfig.TTS_PROXY_URLS?.length || (proxyConfig.TTS_PROXY_URL ? 1 : 0),
        voiceId: voiceId,
        textLength: text.length
      });
    } else if (env && env.DEBUG) {
      console.log(`[PROXY-MODE] 🔄 Proxy-only mode enabled (${proxyConfig.TTS_PROXY_MODE}). Bypassing direct ElevenLabs call, using proxy immediately.`, {
        mode: proxyConfig.TTS_PROXY_MODE,
        availableProxies: proxyConfig.TTS_PROXY_URLS?.length || (proxyConfig.TTS_PROXY_URL ? 1 : 0),
        voiceId: voiceId,
        textLength: text.length,
        timestamp: new Date().toISOString()
      });
    }

    try {
      let proxyResult;

      // 【智能选择】优先使用新的多代理故障转移，向后兼容单代理
      if (proxyConfig.TTS_PROXY_URLS && proxyConfig.TTS_PROXY_URLS.length > 0) {
        // 使用新的多代理故障转移
        proxyResult = await callTtsProxyWithFailover(
          proxyConfig.TTS_PROXY_URLS,
          voiceId,
          payload,
          proxyConfig,
          env,
          context, // <-- 传递上下文
          signal // <-- 传递signal
        );
      } else if (proxyConfig.TTS_PROXY_URL) {
        // 向后兼容：使用原有的单代理方法
        proxyResult = await callVercelProxyFallback(voiceId, payload, proxyConfig, env, context, signal); // <-- 传递上下文和signal
      } else {
        throw new Error('Proxy-only mode enabled but no proxy URLs configured');
      }

      if (env._log) {
        env._log.info('Proxy-only mode successful, returning audio data');
      } else if (env && env.DEBUG) {
        console.log('[PROXY-MODE] ✅ Proxy-only mode successful, returning audio data');
      }

      return proxyResult;
    } catch (proxyError) {
      if (env._log) {
        env._log.error(proxyError, { mode: 'proxy_only' });
      } else {
        console.error('[PROXY-MODE] ❌ Proxy-only mode failed:', proxyError.message);
      }
      // 在仅代理模式下，如果代理失败，就直接抛出错误，不回退到直连
      throw proxyError;
    }
  }

  // --- 路径B: 默认模式（直连优先，失败后回退到代理）---
  const url = `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}?allow_unauthenticated=1`;
  const headers = { 'Content-Type': 'application/json' };

  // 【新增】记录ElevenLabs API请求详情
  if (env._log) {
    env._log.info('TTS Request Details (fallback mode)', {
      url: url,
      method: 'POST',
      voiceId: voiceId,
      modelId: modelId,
      textLength: text.length,
      textPreview: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      payloadSize: JSON.stringify(payload).length
    });
  } else if (env && env.DEBUG) {
    console.log(`[ELEVENLABS-API] 🚀 TTS Request Details (fallback mode):`, {
      url: url,
      method: 'POST',
      headers: headers,
      voiceId: voiceId,
      modelId: modelId,
      textLength: text.length,
      textPreview: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      voice_settings: voice_settings,
      payload: payload,
      payloadSize: JSON.stringify(payload).length + ' bytes',
      timestamp: new Date().toISOString()
    });
  }

  let retries = 3;
  while (retries > 0) {
    try {
      // 【新增】记录每次请求尝试
      if (env._log) {
        env._log.debug(`Sending request (attempt ${4 - retries}/3) to ElevenLabs`, {
          attemptNumber: 4 - retries,
          totalAttempts: 3,
          remainingRetries: retries - 1
        });
      } else if (env && env.DEBUG) {
        console.log(`[ELEVENLABS-API] 📤 Sending request (attempt ${4 - retries}/3) to ElevenLabs:`, {
          attemptNumber: 4 - retries,
          totalAttempts: 3,
          remainingRetries: retries - 1,
          url: url,
          timestamp: new Date().toISOString()
        });
      }

      // 【新增】组合超时信号和快速失败信号
      let combinedSignal;
      if (signal) {
        combinedSignal = AbortSignal.any([signal, AbortSignal.timeout(30000)]);
      } else {
        combinedSignal = AbortSignal.timeout(30000);
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(payload),
        signal: combinedSignal // 【新增】传递AbortSignal以支持快速失败
      });

      if (response.ok) {
        const audioBuffer = await response.arrayBuffer();

        // 【新增】记录成功响应详情
        if (env && env.DEBUG) {
          console.log(`[ELEVENLABS-API] ✅ Request successful:`, {
            status: response.status,
            statusText: response.statusText,
            audioSize: audioBuffer.byteLength + ' bytes',
            audioSizeKB: (audioBuffer.byteLength / 1024).toFixed(2) + ' KB',
            contentType: response.headers.get('content-type'),
            textToAudioRatio: (audioBuffer.byteLength / text.length).toFixed(2) + ' bytes/char',
            timestamp: new Date().toISOString()
          });
        }

        return audioBuffer;
      } else {
        // 【修复】安全地处理非JSON错误响应
        let errorDataText = await response.text(); // 先获取原始文本
        let errorData, errorMessage;

        try {
          // 尝试解析为JSON
          errorData = JSON.parse(errorDataText);
          errorMessage = errorData?.detail?.message || errorData?.message || errorDataText;
        } catch (e) {
          // 如果解析失败，说明不是JSON，直接使用原始文本作为错误信息
          console.error(`[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: ${errorDataText.substring(0, 500)}`);
          errorMessage = `API returned non-JSON response (status: ${response.status}): ${errorDataText.substring(0, 200)}`;
          errorData = { message: errorMessage }; // 创建一个简单的错误对象
        }

        // 【修复】从 detail 对象中正确提取错误消息 (保留原有逻辑，但基于安全的errorData)
        const finalErrorMessage = errorData?.detail?.message || errorData?.message || JSON.stringify(errorData) || 'Failed to generate speech';

        // 【新增】记录错误响应详情
        if (env && env.DEBUG) {
          console.error(`[ELEVENLABS-API] ❌ Request failed:`, {
            status: response.status,
            statusText: response.statusText,
            errorMessage: finalErrorMessage,
            errorDetails: errorData,
            detailStatus: errorData?.detail?.status,
            attemptNumber: 4 - retries,
            willRetry: retries > 1,
            rawErrorResponse: errorDataText.substring(0, 500), // 记录原始响应供调试
            timestamp: new Date().toISOString()
          });
        }

        // 【新增】检测违规内容并设置标志
        if (isContentViolationError(response.status, errorData, finalErrorMessage)) {
          const violationError = new Error(finalErrorMessage);
          violationError.status = response.status;
          violationError.isContentViolation = true; // 【关键标志】
          violationError.isDataCenterRetryable = false;
          violationError.originalError = errorData;
          throw violationError;
        }

        // 【修复】创建增强的错误对象，使用更可靠的 finalErrorMessage
        const enhancedError = new Error(finalErrorMessage);
        enhancedError.status = response.status;
        enhancedError.isDataCenterRetryable = isDataCenterRetryableError(enhancedError, response.status, errorData);
        enhancedError.originalError = errorData;

        throw enhancedError;
      }
    } catch (error) {
      // 【新增】检测违规错误并立即终止重试
      if (error.isContentViolation) {
        if (env._log) {
          env._log.warn('Content violation detected in direct API call. Terminating retries.', {
            error: error.message,
            isContentViolation: true
          });
        } else if (env && env.DEBUG) {
          console.warn('[ELEVENLABS-API] Content violation detected. Terminating retries.', {
            error: error.message,
            isContentViolation: true
          });
        }
        throw error; // 立即抛出，不进行重试
      }

      retries--;

      // 【新增】记录重试信息，计算动态延迟时间
      let retryDelayInfo = 'none';
      if (retries > 0) {
        if (proxyConfig.TTS_ENABLE_BACKOFF) {
          const failedAttemptNumber = 4 - retries;
          const baseDelay = Math.pow(2, failedAttemptNumber - 1) * 1000;
          const estimatedDelay = Math.min(baseDelay + 250, proxyConfig.TTS_DIRECT_MAX_DELAY); // 使用抖动中位数估算
          retryDelayInfo = `~${estimatedDelay.toFixed(0)}ms (exponential backoff)`;
        } else {
          retryDelayInfo = '1000ms (fixed delay)';
        }
      }

      if (env._log) {
        env._log.warn(`Request error, ${retries > 0 ? 'retrying' : 'giving up'}`, {
          error: error.message,
          errorType: error.constructor.name,
          attemptNumber: 4 - retries - 1,
          remainingRetries: retries,
          willRetry: retries > 0,
          retryDelay: retryDelayInfo
        });
      } else if (env && env.DEBUG) {
        console.warn(`[ELEVENLABS-API] ⚠️ Request error, ${retries > 0 ? 'retrying' : 'giving up'}:`, {
          error: error.message,
          errorType: error.constructor.name,
          attemptNumber: 4 - retries - 1,
          remainingRetries: retries,
          willRetry: retries > 0,
          retryDelay: retryDelayInfo,
          timestamp: new Date().toISOString()
        });
      }

      if (retries === 0) {
        // 【修改】所有直接重试都失败了，检查是否应该尝试代理（使用之前获取的proxyConfig）
        if (shouldAttemptProxy(error, proxyConfig)) {
          if (proxyConfig.ENABLE_PROXY_DEBUG) {
            console.warn('[PROXY-TRIGGER] Direct ElevenLabs calls failed with retryable error. Triggering proxy failover...', {
              originalError: error.message,
              errorStatus: error.status,
              isDataCenterRetryable: error.isDataCenterRetryable,
              availableProxies: proxyConfig.TTS_PROXY_URLS?.length || (proxyConfig.TTS_PROXY_URL ? 1 : 0),
              timestamp: new Date().toISOString()
            });
          }

          try {
            let fallbackResult;

            // 【智能选择】优先使用新的多代理故障转移，向后兼容单代理
            if (proxyConfig.TTS_PROXY_URLS && proxyConfig.TTS_PROXY_URLS.length > 0) {
              // 使用新的多代理故障转移
              fallbackResult = await callTtsProxyWithFailover(
                proxyConfig.TTS_PROXY_URLS,
                voiceId,
                payload,
                proxyConfig,
                env,
                context, // <-- 传递上下文
                signal // <-- 传递signal
              );
            } else {
              // 向后兼容：使用原有的单代理方法
              fallbackResult = await callVercelProxyFallback(voiceId, payload, proxyConfig, env, context, signal); // <-- 传递上下文和signal
            }

            if (proxyConfig.ENABLE_PROXY_DEBUG) {
              console.log('[PROXY-SUCCESS] ✅ Proxy failover successful, returning audio data');
            }

            return fallbackResult;
          } catch (proxyError) {
            if (proxyConfig.ENABLE_PROXY_DEBUG) {
              console.error('[PROXY-FAILED] ❌ Proxy failover also failed:', {
                originalError: error.message,
                proxyError: proxyError.message,
                timestamp: new Date().toISOString()
              });
            }

            // 【增强日志】记录代理故障转移完全失败
            if (env._log) {
              env._log.error(proxyError, {
                operation: 'proxy-failover-complete-failure',
                originalError: error.message,
                proxyError: proxyError.message,
                voiceId: voiceId,
                textLength: payload.text?.length,
                availableProxies: proxyConfig.TTS_PROXY_URLS?.length || (proxyConfig.TTS_PROXY_URL ? 1 : 0)
              });
            }

            // 【关键修复】优先检查代理错误是否为违规，如果是则直接抛出保持标志完整
            if (proxyError.isContentViolation) {
              if (proxyConfig.ENABLE_PROXY_DEBUG) {
                console.warn('[PROXY-VIOLATION] Proxy returned content violation. Prioritizing violation error over direct API error.', {
                  directError: error.message,
                  proxyViolationError: proxyError.message,
                  isContentViolation: true
                });
              }
              // 直接抛出代理的违规错误，保持isContentViolation标志
              throw proxyError;
            }

            // 【关键修复】增强错误合并逻辑，确保代理故障转移失败能被正确处理
            const combinedError = new Error(`Both direct and proxy failed. Direct: ${error.message}, Proxy: ${proxyError.message}`);
            combinedError.originalError = error;
            combinedError.proxyError = proxyError;
            combinedError.status = proxyError.status || error.status || 500;
            combinedError.isProxyFailoverFailure = true;

            // 【重要】保持数据中心重试标志，但只有在代理错误不是内容违规时
            if (error.isDataCenterRetryable && !proxyError.isContentViolation) {
              combinedError.isDataCenterRetryable = true;
            }

            // 【新增】如果代理错误也是数据中心级别的可重试错误，保持该标志
            if (proxyError.isDataCenterRetryable && !proxyError.isContentViolation) {
              combinedError.isDataCenterRetryable = true;
            }

            if (proxyConfig.ENABLE_PROXY_DEBUG) {
              console.error('[PROXY-COMBINED-ERROR] Created combined error:', {
                message: combinedError.message,
                isDataCenterRetryable: combinedError.isDataCenterRetryable,
                isProxyFailoverFailure: combinedError.isProxyFailoverFailure,
                status: combinedError.status
              });
            }

            throw combinedError;
          }
        }

        // 不满足代理条件或代理未配置，抛出原始错误
        throw error;
      }

      // 【新增】指数退避 + 抖动机制，与代理重试逻辑保持一致
      if (proxyConfig.TTS_ENABLE_BACKOFF) {
        // 计算这是第几次失败 (第一次失败是 1, 第二次是 2, 第三次是 3)
        const failedAttemptNumber = 4 - retries;

        // 计算基础延迟时间 (1s, 2s, 4s...)
        const baseDelay = Math.pow(2, failedAttemptNumber - 1) * 1000;

        // 添加随机抖动 (0-500ms)，与代理集群重试保持一致
        const jitter = Math.random() * 500;

        // 计算总延迟，设置上限防止等待时间过长
        const totalDelay = Math.min(baseDelay + jitter, proxyConfig.TTS_DIRECT_MAX_DELAY);

        // 【新增】记录重试延迟信息，与代理重试日志格式保持一致
        if (env._log) {
          env._log.warn(`[ELEVENLABS-RETRY] Request failed. Retrying in ${totalDelay.toFixed(0)}ms...`, {
            failedAttempt: failedAttemptNumber,
            baseDelay: baseDelay,
            jitter: jitter.toFixed(0),
            totalDelay: totalDelay.toFixed(0),
            maxDelay: proxyConfig.TTS_DIRECT_MAX_DELAY
          });
        } else if (env && env.DEBUG) {
          console.warn(`[ELEVENLABS-RETRY] ⏳ Request failed. Retrying in ${totalDelay.toFixed(0)}ms...`, {
            failedAttempt: failedAttemptNumber,
            baseDelay: baseDelay,
            jitter: jitter.toFixed(0),
            totalDelay: totalDelay.toFixed(0),
            maxDelay: proxyConfig.TTS_DIRECT_MAX_DELAY,
            timestamp: new Date().toISOString()
          });
        }

        await new Promise(resolve => setTimeout(resolve, totalDelay));
      } else {
        // 【向后兼容】如果禁用了指数退避，保持原有的固定延迟
        if (env._log) {
          env._log.warn('[ELEVENLABS-RETRY] Request failed. Retrying in 1000ms (backoff disabled)...');
        } else if (env && env.DEBUG) {
          console.warn('[ELEVENLABS-RETRY] ⏳ Request failed. Retrying in 1000ms (backoff disabled)...', {
            timestamp: new Date().toISOString()
          });
        }

        await new Promise(resolve => setTimeout(resolve, 1000)); // 保持原有固定1秒延迟
      }
    }
  }
}

// ========== 并发计算函数 ==========

// 备份：原始的复杂并发计算函数（已废弃，保留用于回滚）
function calculateOptimalConcurrency_BACKUP_COMPLEX(chunkCount) {
  // ❶ 以 "50 个子请求" 为硬上限，扣除 5 作缓冲
  const maxSubRequests = 45;
  const retryBuffer = Math.ceil(chunkCount * 0.1);     // 10% 留给重试
  const hardCap = Math.floor((maxSubRequests - retryBuffer) / chunkCount);

  // ❂ 经验阶梯（确保中小任务速度）
  const softCap =
    chunkCount <= 5  ? 6 :
    chunkCount <= 15 ? 4 :
    chunkCount <= 30 ? 2 : 1;

  // ❸ 取两者较小值，再 clamp 到 1‒6
  const optimalConcurrency = Math.max(1, Math.min(6, Math.min(hardCap, softCap)));

  console.log(`[CONCURRENCY-BACKUP] Calculated optimal concurrency for ${chunkCount} chunks:`, {
    chunkCount: chunkCount,
    retryBuffer: retryBuffer,
    hardCap: hardCap,
    softCap: softCap,
    finalConcurrency: optimalConcurrency,
    estimatedSubRequests: chunkCount + retryBuffer,
    safetyMargin: maxSubRequests - (chunkCount + retryBuffer)
  });

  return optimalConcurrency;
}

// 优化后的并发计算函数 - 简化最优方案
function calculateOptimalConcurrency(chunkCount, env) {
  const CF_CONN_LIMIT = 6; // Cloudflare Workers 每请求并发连接限制

  // 兜底安全检查（在当前约束下几乎不会触发，但保留是好习惯）
  const estimatedSubRequests = chunkCount + Math.ceil(chunkCount * 0.1); // 估算 chunk + 10% 重试
  if (estimatedSubRequests >= 45) { // 留一些余量给KV操作
      console.warn(`[CONCURRENCY] High chunk count (${chunkCount}) is approaching subrequest limit. Consider splitting the task.`);
  }

  // 核心逻辑：直接使用最大并发，但不超过任务本身的数量
  const concurrency = Math.min(CF_CONN_LIMIT, chunkCount);

  if (env.DEBUG) {
    console.log(`[CONCURRENCY] Calculated optimal concurrency for ${chunkCount} chunks: ${concurrency} (Strategy: Simple Max)`, {
      chunkCount: chunkCount,
      maxConcurrency: CF_CONN_LIMIT,
      finalConcurrency: concurrency,
      estimatedSubRequests: estimatedSubRequests,
      estimatedRounds: Math.ceil(chunkCount / concurrency),
      performanceGain: chunkCount > 6 ? `~${Math.round((1 - Math.ceil(chunkCount / concurrency) / Math.ceil(chunkCount / Math.min(chunkCount <= 5 ? 6 : chunkCount <= 15 ? 4 : chunkCount <= 30 ? 2 : 1, chunkCount))) * 100)}% faster` : 'baseline'
    });
  } else {
    // 在非DEBUG模式下，只打印最关键的信息
    console.log(`[CONCURRENCY] Using ${concurrency} concurrent requests for ${chunkCount} chunks`);
  }

  return concurrency;
}

// 轻量级并发控制器 - 无外部依赖
function createConcurrencyLimiter(maxConcurrent) {
  let running = 0;
  const queue = [];

  return function limit(fn) {
    return new Promise((resolve, reject) => {
      const execute = async () => {
        running++;
        try {
          const result = await fn();
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          running--;
          if (queue.length > 0) {
            const next = queue.shift();
            next();
          }
        }
      };

      if (running < maxConcurrent) {
        execute();
      } else {
        queue.push(execute);
      }
    });
  };
}

// 优化后的并发处理函数 - 支持部分失败容错和动态并发
async function processChunks(chunks, voiceId, modelId, stability, similarity_boost, style, speed, env, context = {}) {
  // 【使用新的日志系统】如果env中有日志上下文，使用它
  if (env._log) {
    env._log.info(`Processing ${chunks.length} chunks with dynamic concurrency...`, {
      taskId: context.taskId || 'N/A',
      username: context.username || 'N/A'
    });
  } else {
    console.log(`[CONCURRENCY] Processing ${chunks.length} chunks with dynamic concurrency...`);
  }

  // 【新增】创建AbortController用于快速失败
  const abortController = new AbortController();
  let firstViolationError = null;

  // 动态计算最优并发数，智能守卫子请求限制
  const optimalConcurrency = calculateOptimalConcurrency(chunks.length, env);
  const limiter = createConcurrencyLimiter(optimalConcurrency);

  if (env._log) {
    env._log.debug(`Using ${optimalConcurrency} concurrent requests for ${chunks.length} chunks`, {
      optimalConcurrency,
      chunkCount: chunks.length
    });
  } else if (env.DEBUG) {
    console.log(`[CONCURRENCY] Using ${optimalConcurrency} concurrent requests for ${chunks.length} chunks`);
  }

  // 创建所有任务，每个任务包含索引以保证顺序
  const tasks = chunks.map((chunk, index) =>
    limiter(async () => {
      // 【新增】检查是否已被中止
      if (abortController.signal.aborted) {
        throw new Error(`Chunk ${index + 1} cancelled due to violation`);
      }

      try {
        // 【优化】为每个分片创建更精细的上下文
        let chunkEnv = env;
        if (env._logContext) {
          const chunkLogContext = {
            ...env._logContext,
            chunkIndex: `${index + 1}/${chunks.length}`
          };
          chunkEnv = enhanceEnvWithLogging(env, chunkLogContext);

          chunkEnv._log.debug(`Processing chunk`, {
            chunkLength: chunk.length,
            textPreview: chunk.substring(0, 50) + (chunk.length > 50 ? '...' : '')
          });
        } else if (env.DEBUG) {
          console.log(`Processing chunk ${index + 1}/${chunks.length}, length: ${chunk.length}`);
        }

        // 【修改点】传递中止信号给 generateSpeech
        const audioData = await generateSpeech(chunk, voiceId, modelId, stability, similarity_boost, style, speed, chunkEnv, context, abortController.signal);
        if (!audioData) {
          throw new Error(`Failed to generate audio for chunk ${index + 1}`);
        }
        return { index, audioData, success: true };
      } catch (error) {
        // 【新增】检测违规并立即中止
        if (error.isContentViolation && !firstViolationError) {
          firstViolationError = error;
          abortController.abort(); // 立即中止所有其他任务

          if (env._log) {
            env._log.warn('[FAST-FAIL] Violation detected, aborting all chunks...', {
              chunkIndex: index + 1,
              error: error.message
            });
          } else if (env.DEBUG) {
            console.warn(`[FAST-FAIL] Violation detected in chunk ${index + 1}, aborting all chunks...`);
          }
        }

        if (env._logContext) {
          // 使用分片级别的上下文记录错误
          const chunkLogContext = {
            ...env._logContext,
            chunkIndex: `${index + 1}/${chunks.length}`
          };
          const chunkLogger = createLogger(env);
          chunkLogger.error(error, chunkLogContext, {
            chunkLength: chunk.length,
            textPreview: chunk.substring(0, 50) + (chunk.length > 50 ? '...' : '')
          });
        } else {
          console.error(`Error processing chunk ${index + 1}:`, error);
        }
        // 【重要】把完整的 error 对象返回，而不是只返回 error.message
        return { index, error: error, success: false };
      }
    })
  );

  // 使用 Promise.allSettled 确保所有任务都完成（成功或失败）
  const results = await Promise.allSettled(tasks);

  // 【新增】优先检查违规错误并立即传播
  if (firstViolationError) {
    if (env._log) {
      env._log.warn('[FAST-FAIL] Propagating violation error', {
        error: firstViolationError.message,
        isContentViolation: true
      });
    } else if (env.DEBUG) {
      console.warn('[FAST-FAIL] Propagating violation error:', firstViolationError.message);
    }
    throw firstViolationError; // 立即传播，跳过所有其他处理
  }

  // 提取实际结果，处理 Promise.allSettled 的包装
  const processedResults = results.map(result => {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      // 这种情况很少见，通常是limiter内部错误
      console.error('Task execution failed:', result.reason);
      return { index: -1, error: result.reason.message, success: false };
    }
  });

  // 按索引排序确保音频顺序正确
  processedResults.sort((a, b) => a.index - b.index);

  // 分离成功和失败的结果
  const successfulResults = processedResults.filter(result => result.success);
  const failedResults = processedResults.filter(result => !result.success);

  console.log(`Processing completed: ${successfulResults.length} successful, ${failedResults.length} failed`);

  // 如果有失败的chunk，记录详细信息
  if (failedResults.length > 0) {
    console.warn('Failed chunks:', failedResults.map(r => `Chunk ${r.index + 1}: ${r.error}`));
  }

  // 【修复】检查是否有任何一个失败是可重试的数据中心错误
  if (failedResults.length > 0) {
    const firstRetryableError = failedResults.find(r => r.error?.isDataCenterRetryable)?.error;

    if (firstRetryableError) {
      // 如果找到了可重试的错误，就优先把它抛出去，并附加上下文信息
      console.log(`[PROCESSSCHUNKS] Found retryable datacenter error in chunk processing, propagating for retry`);
      const overallError = new Error(`Data center retryable error detected: ${firstRetryableError.message}`);
      overallError.isDataCenterRetryable = true; // 明确传递标志
      overallError.originalStatus = firstRetryableError.status;
      overallError.originalError = firstRetryableError.originalError;
      throw overallError;
    }
  }

  // 根据失败比例决定处理策略
  const failureRate = failedResults.length / chunks.length;

  if (failureRate >= 0.5) {
    // 如果失败率很高，但没有一个是可重试的错误，则抛出通用错误
    throw new Error(`Too many chunks failed (${failedResults.length}/${chunks.length}). This may indicate a non-retryable system issue. Failed chunks: ${failedResults.map(r => r.index + 1).join(', ')}`);
  } else if (failedResults.length > 0) {
    // 如果有部分失败，但失败率 < 50%，尝试重试失败的chunk
    console.log(`Retrying ${failedResults.length} failed chunks...`);

    // 【修复】将finalFailedResults声明移到try块外面
    let finalFailedResults = [];

    try {
      const retryResults = await retryFailedChunks(failedResults, chunks, voiceId, modelId, stability, similarity_boost, style, speed, limiter, env, context);

      // 合并重试结果
      retryResults.forEach(retryResult => {
        if (retryResult.success) {
          successfulResults.push(retryResult);
          if (env.DEBUG) {
            console.log(`Retry successful for chunk ${retryResult.index + 1}`);
          }
        } else {
          console.warn(`Retry failed for chunk ${retryResult.index + 1}: ${retryResult.error}`);
          // 收集最终失败的结果
          finalFailedResults.push(retryResult);
        }
      });
    } catch (error) {
      // 【新增】捕获重试阶段的违规错误并立即传播
      if (error.isContentViolation) {
        if (env._log) {
          env._log.warn('[PROCESSSCHUNKS] Content violation detected during retry phase, immediately terminating', {
            error: error.message,
            isContentViolation: true
          });
        } else if (env.DEBUG) {
          console.warn('[PROCESSSCHUNKS] Content violation detected during retry phase, immediately terminating:', error.message);
        }
        throw error; // 立即传播违规错误，跳过所有后续处理
      }

      // 其他错误继续抛出
      throw error;
    }

    // 重新排序
    successfulResults.sort((a, b) => a.index - b.index);

    // 【修复】检查重试后是否还有可重试的错误
    if (finalFailedResults.length > 0) {
      const retryableErrorAfterRetry = finalFailedResults.find(r => r.error?.isDataCenterRetryable)?.error;

      if (retryableErrorAfterRetry) {
        console.log(`[PROCESSSCHUNKS] Found retryable datacenter error even after retry, propagating for datacenter switch`);
        const overallError = new Error(`Data center retryable error persists after retry: ${retryableErrorAfterRetry.message}`);
        overallError.isDataCenterRetryable = true;
        overallError.originalStatus = retryableErrorAfterRetry.status;
        overallError.originalError = retryableErrorAfterRetry.originalError;
        throw overallError;
      }
    }
  }

  // 检查是否还有失败的chunk
  const finalFailedCount = chunks.length - successfulResults.length;
  if (finalFailedCount > 0) {
    const missingChunks = [];
    for (let i = 0; i < chunks.length; i++) {
      if (!successfulResults.find(r => r.index === i)) {
        missingChunks.push(i + 1);
      }
    }
    console.warn(`Final result: ${finalFailedCount} chunks still failed after retry: ${missingChunks.join(', ')}`);

    // 如果最终失败的chunk太多，抛出错误
    if (finalFailedCount / chunks.length >= 0.3) {
      throw new Error(`Too many chunks failed even after retry (${finalFailedCount}/${chunks.length}). Failed chunks: ${missingChunks.join(', ')}`);
    }
  }

  // 提取音频数据
  const audioDataList = successfulResults.map(result => result.audioData);

  console.log(`Successfully processed ${audioDataList.length}/${chunks.length} chunks concurrently`);
  return audioDataList;
}

// 重试失败chunk的辅助函数
async function retryFailedChunks(failedResults, originalChunks, voiceId, modelId, stability, similarity_boost, style, speed, limiter, env, context = {}) {
  // 【新增】创建AbortController用于重试阶段的快速失败
  const retryAbortController = new AbortController();
  let firstViolationError = null;

  if (env._log) {
    env._log.debug(`Starting retry for ${failedResults.length} failed chunks with fast-fail support`, {
      failedChunkCount: failedResults.length,
      taskId: context.taskId || 'N/A'
    });
  } else if (env.DEBUG) {
    console.log(`[RETRY-FAST-FAIL] Starting retry for ${failedResults.length} failed chunks with fast-fail support`);
  }

  const retryTasks = failedResults.map(failedResult =>
    limiter(async () => {
      // 【新增】检查是否已被中止
      if (retryAbortController.signal.aborted) {
        throw new Error(`Retry chunk ${failedResult.index + 1} cancelled due to violation in another retry`);
      }

      try {
        if (env.DEBUG) {
          console.log(`Retrying chunk ${failedResult.index + 1}...`);
        }
        const chunk = originalChunks[failedResult.index];

        // 【关键修改】传递signal参数给generateSpeech，启用重试阶段的快速失败
        const audioData = await generateSpeech(chunk, voiceId, modelId, stability, similarity_boost, style, speed, env, context, retryAbortController.signal);

        if (!audioData) {
          throw new Error(`Retry failed to generate audio for chunk ${failedResult.index + 1}`);
        }
        return { index: failedResult.index, audioData, success: true };
      } catch (error) {
        // 【新增】检测违规并立即中止所有重试
        if (error.isContentViolation && !firstViolationError) {
          firstViolationError = error;
          retryAbortController.abort(); // 立即中止所有其他重试任务

          if (env._log) {
            env._log.warn('[RETRY-FAST-FAIL] Violation detected during retry, aborting all retry tasks...', {
              chunkIndex: failedResult.index + 1,
              error: error.message
            });
          } else if (env.DEBUG) {
            console.warn(`[RETRY-FAST-FAIL] Violation detected in retry chunk ${failedResult.index + 1}, aborting all retry tasks...`);
          }
        }

        console.error(`Retry failed for chunk ${failedResult.index + 1}:`, error);
        // 【修复】保留错误对象的完整信息，包括isDataCenterRetryable标志
        return { index: failedResult.index, error: error, success: false };
      }
    })
  );

  const retryResults = await Promise.allSettled(retryTasks);

  // 【新增】优先检查违规错误并立即传播
  if (firstViolationError) {
    if (env._log) {
      env._log.warn('[RETRY-FAST-FAIL] Propagating violation error from retry phase', {
        error: firstViolationError.message,
        isContentViolation: true
      });
    } else if (env.DEBUG) {
      console.warn('[RETRY-FAST-FAIL] Propagating violation error from retry phase:', firstViolationError.message);
    }
    throw firstViolationError; // 立即传播，跳过所有其他处理
  }

  return retryResults.map(result => result.status === 'fulfilled' ? result.value : { success: false, error: result.reason.message });
}

// ========== R2异步任务处理相关函数 ==========

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 计算用户配额详细信息
 * @param {object} userData - 用户数据
 * @returns {object} 配额详细信息
 */
function calculateQuotaDetails(userData) {
  const vip = userData.vip || { expireAt: 0 };

  // 判断是否为老用户（没有quotaChars字段）
  const isLegacyUser = vip.quotaChars === undefined;

  if (isLegacyUser) {
    // 老用户：无限制
    return {
      isLegacyUser: true,
      quotaChars: undefined,
      usedChars: undefined,
      remainingChars: undefined,
      usagePercentage: 0
    };
  }

  // 新用户：计算具体配额信息
  const totalQuota = vip.quotaChars || 0;
  const usedQuota = vip.usedChars || 0;
  const remainingQuota = Math.max(0, totalQuota - usedQuota);
  const usagePercentage = totalQuota > 0 ? Math.min(100, (usedQuota / totalQuota) * 100) : 0;

  return {
    isLegacyUser: false,
    quotaChars: totalQuota,
    usedChars: usedQuota,
    remainingChars: remainingQuota,
    usagePercentage: Math.round(usagePercentage * 100) / 100 // 保留2位小数
  };
}







// 存储音频文件到R2
async function storeAudioFile(taskId, audioBuffer, env) {
  const key = `audios/${taskId}.mp3`;
  const audioSize = audioBuffer.byteLength;

  if (env.DEBUG) {
    console.log(`[R2-AUDIO] Storing audio for task ${taskId}:`, {
      audioSize: audioSize,
      audioType: audioBuffer.constructor.name,
      bucketName: 'AUDIOS',
      key: key,
      sizeInMB: (audioSize / 1024 / 1024).toFixed(2)
    });
  }

  try {
    const startTime = Date.now();

    const result = await env.AUDIOS.put(key, audioBuffer, {
      httpMetadata: {
        contentType: 'audio/mpeg',
        cacheControl: 'public, max-age=86400', // 1天缓存
        contentDisposition: `attachment; filename="${generateDateBasedFilename()}"` // 强制下载
      }
    });

    const uploadTime = Date.now() - startTime;

    if (env.DEBUG) {
      console.log(`[R2-AUDIO] ✅ Successfully stored audio for task ${taskId}:`, {
        audioSize: audioSize,
        uploadTimeMs: uploadTime,
        sizeInMB: (audioSize / 1024 / 1024).toFixed(2),
        timestamp: new Date().toISOString()
      });
    } else {
      console.log(`[R2-AUDIO] ✅ Audio stored for task ${taskId}, size: ${(audioSize / 1024 / 1024).toFixed(2)}MB`);
    }

    return result;
  } catch (error) {
    console.error(`[R2-AUDIO] ❌ Failed to store audio for task ${taskId}:`, {
      error: error.message,
      audioSize: audioSize,
      key: key
    });
    throw error;
  }
}



/**
 * 【改进版】智能配额验证 - 支持配置驱动的模式选择
 * @param {string} username - 用户名
 * @param {object} env - Cloudflare环境变量
 * @param {('STANDARD'|'PRO')} [requiredTier='STANDARD'] - 要求最低的会员等级
 * @param {number} [requestedChars=0] - 本次任务请求的字符数，0表示不检查配额
 *
 * 核心逻辑：
 * - 如果MAIN_BACKEND_BASE + B_BACKEND_API_TOKEN都配置：采用严格模式，使用B后端API
 *   * API失败时任务失败，确保配额数据的完全一致性和准确性
 * - 如果未配置或配置不完整：自动降级到KV存储模式
 *   * 使用本地KV存储进行配额验证，支持完全离线部署
 * - 智能模式选择：提供最大的部署灵活性
 */
async function checkVip(username, env, requiredTier = 'STANDARD', requestedChars = 0) {
  // 【新增】智能模式选择
  if (isBBackendApiEnabled(env)) {
    // 配置了完整的B后端API，使用严格模式
    console.log(`[QUOTA-CHECK] Using B Backend API (strict mode) for user ${username}, tier: ${requiredTier}, chars: ${requestedChars}`);

    try {
      const result = await callBBackendApi('/users/check-quota', {
        username: username,
        requiredTier: requiredTier,
        requestedChars: requestedChars
      }, env);

      console.log(`[QUOTA-CHECK] B Backend API check passed for user ${username}`);
      return result;
    } catch (error) {
      console.error(`[QUOTA-CHECK] B Backend API failed for user ${username}:`, error.message);
      // 严格模式：API失败时直接让任务失败，确保不会超额使用
      console.error(`[QUOTA-CHECK] Quota verification failed, task will be rejected for user ${username}`);
      throw new Error(`配额验证失败：无法连接到配额服务。${error.message}`, { cause: 'quota' });
    }
  } else {
    // 未配置B后端API，使用KV存储模式
    console.log(`[QUOTA-CHECK] Using KV storage mode for user ${username}, tier: ${requiredTier}, chars: ${requestedChars}`);
    return await checkVipKVMode(username, env, requiredTier, requestedChars);
  }
}

/**
 * KV存储模式的VIP权限检查（原有逻辑）
 * @param {string} username - 用户名
 * @param {object} env - Cloudflare环境变量
 * @param {('STANDARD'|'PRO')} [requiredTier='STANDARD'] - 要求最低的会员等级
 * @param {number} [requestedChars=0] - 本次任务请求的字符数，0表示不检查配额
 */
async function checkVipKVMode(username, env, requiredTier = 'STANDARD', requestedChars = 0) {
  const userDataString = await env.USERS.get(`user:${username}`);
  if (!userDataString) {
    throw new Error('用户不存在', { cause: 'quota' });
  }
  const userData = JSON.parse(userDataString);
  const vip = userData.vip;

  // 1. 基础检查：是否有会员资格
  if (!vip) {
    throw new Error('请先开通会员', { cause: 'quota' });
  }

  // 2. 时间检查：会员是否已过期
  if (Date.now() > vip.expireAt) {
    throw new Error('会员已过期，请续费', { cause: 'quota' });
  }

  // --- START: 老新用户区分的字符数配额检查 ---

  // 3. 字符数配额检查 (仅对新规则用户生效)
  // 通过检查 vip.quotaChars 是否存在，来判断是否为新规则用户
  const isNewRuleUser = vip.quotaChars !== undefined;

  if (isNewRuleUser && requestedChars > 0) {
    // 这是一名受配额限制的用户（新用户或已续费的老用户）
    console.log(`[QUOTA-CHECK] User ${username} is under new quota rule. Checking quota...`);

    const currentUsed = vip.usedChars || 0;
    const totalQuota = vip.quotaChars || 0; // totalQuota 必然存在

    if (currentUsed + requestedChars > totalQuota) {
      const remaining = Math.max(0, totalQuota - currentUsed);
      throw new Error(`字符数配额不足。剩余 ${remaining} 字符，本次需要 ${requestedChars} 字符。请升级或续费套餐。`, { cause: 'quota' });
    }
  } else if (requestedChars > 0) {
    // 这是老用户，享受无限字符权益
    console.log(`[QUOTA-CHECK] User ${username} is a legacy user. Skipping quota check.`);
  }

  // --- END: 老新用户区分的字符数配额检查 ---

  // 3. 等级检查：如果要求PRO权限
  if (requiredTier === 'PRO') {
    const userTier = vip.type;
    if (!userTier || !userTier.startsWith('P')) {
      throw new Error('此功能需要PRO会员权限', { cause: 'quota' });
    }
  }

  // 4. 测试套餐的特殊逻辑 (保持不变)
  if (vip.type === 'T') {
    const remainingTime = Math.max(0, vip.expireAt - Date.now()) / 1000;
    if (remainingTime <= 0) {
      throw new Error('测试时间已用完，请充值', { cause: 'quota' });
    }
    console.log(`测试套餐剩余时间: ${remainingTime.toFixed(1)}秒`);
  }
}








// 处理TTS相关的GET请求（状态查询和下载）
async function handleTTS(request, username, env) {
  await checkVip(username, env);



  // 处理状态检查请求 GET /api/tts/status/{taskId}
  if (request.method === 'GET' && request.url.includes('/status/')) {
    const url = new URL(request.url);
    const taskId = url.pathname.split('/status/')[1];

    if (env.DEBUG) {
      console.log(`[STATUS-CHECK] 📋 Received status check request:`, {
        taskId: taskId,
        username: username,
        url: request.url,
        timestamp: new Date().toISOString()
      });
    }

    if (!taskId) {
      console.error(`[STATUS-CHECK] ❌ Missing task ID in request`);
      return new Response(JSON.stringify({ error: 'Task ID is required' }), {
        status: 400,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }

    try {
      const taskStatus = await getStatusKV(env, taskId);

      if (!taskStatus) {
        console.warn(`[STATUS-CHECK] ⚠️ Task not found: ${taskId}`);
        return new Response(JSON.stringify({ error: 'Task not found' }), {
          status: 404,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 【优化】智能超时检测机制 - 支持环境变量配置和动态调整
      const now = Date.now();
      const taskAge = now - taskStatus.createdAt;
      const timeoutConfig = getSmartTimeoutConfig(env);

      // 根据任务阶段设置不同的超时时间
      let timeoutMs;
      let timeoutReason;
      let timeoutDetails = null; // 用于存储详细的超时计算信息

      switch (taskStatus.currentStep) {
        case 'initialization':
        case 'storing_initial_status':
          timeoutMs = timeoutConfig.INIT_TIMEOUT;
          timeoutReason = 'Task initialization timeout';
          break;
        case 'text_processing':
          timeoutMs = timeoutConfig.TEXT_PROCESSING_TIMEOUT;
          timeoutReason = 'Text processing timeout';
          break;
        case 'audio_generation':
          // 【核心优化】使用智能超时计算函数
          const audioTimeoutResult = calculateAudioGenerationTimeout(taskStatus, env);
          timeoutMs = audioTimeoutResult.timeoutMs;
          timeoutDetails = audioTimeoutResult.details;
          timeoutReason = `Audio generation timeout (${timeoutDetails.chunkCount} chunks, ${Math.round(timeoutDetails.adjustedChunkTimeout/1000)}s per chunk)`;

          // 【调试日志】如果启用了超时调试，记录详细计算过程
          if (timeoutConfig.ENABLE_TIMEOUT_DEBUG) {
            console.log(`[TIMEOUT-DEBUG] Audio generation timeout calculated for task ${taskId}:`, {
              taskAge: Math.round(taskAge / 1000),
              calculatedTimeout: Math.round(timeoutMs / 1000),
              details: timeoutDetails
            });
          }
          break;
        case 'audio_merging':
          timeoutMs = timeoutConfig.AUDIO_MERGING_TIMEOUT;
          timeoutReason = 'Audio merging timeout';
          break;
        case 'r2_storage':
          timeoutMs = timeoutConfig.R2_STORAGE_TIMEOUT;
          timeoutReason = 'Storage timeout';
          break;
        default:
          timeoutMs = timeoutConfig.DEFAULT_TIMEOUT;
          timeoutReason = 'Task timeout';
      }

      if (taskStatus.status === 'processing' && taskAge > timeoutMs) {
        // 【优化】增强超时检测日志，包含智能计算的详情
        const timeoutLogData = {
          taskAge: taskAge,
          timeoutMs: timeoutMs,
          currentStep: taskStatus.currentStep,
          reason: timeoutReason
        };

        // 如果是音频生成阶段，添加详细的计算信息
        if (timeoutDetails) {
          timeoutLogData.smartTimeoutDetails = timeoutDetails;
        }

        console.warn(`[STATUS-CHECK] ⏰ Task ${taskId} timeout detected:`, timeoutLogData);

        // 【优化】更新状态为超时失败，包含智能超时的详细信息
        try {
          const timeoutStatusData = {
            ...taskStatus,
            status: 'failed',
            error: timeoutReason,
            failedAt: now,
            failedAtStep: taskStatus.currentStep || 'unknown',
            timeoutDetails: {
              taskAge: taskAge,
              timeoutMs: timeoutMs,
              currentStep: taskStatus.currentStep,
              // 【新增】如果有智能计算详情，也保存到状态中
              ...(timeoutDetails && { smartCalculation: timeoutDetails })
            }
          };

          await storeStatusKV(env, taskId, timeoutStatusData);
          console.log(`[STATUS-CHECK] ✅ Timeout status updated for task ${taskId}`);
        } catch (timeoutUpdateError) {
          console.error(`[STATUS-CHECK] ❌ Failed to update timeout status for task ${taskId}:`, timeoutUpdateError);
        }

        // 【优化】返回更详细的超时响应
        const timeoutResponse = {
          taskId: taskId,
          status: 'failed',
          error: timeoutReason,
          failedAtStep: taskStatus.currentStep || 'unknown',
          timeoutDetails: {
            taskAge: Math.round(taskAge / 1000),
            timeoutSeconds: Math.round(timeoutMs / 1000),
            // 如果有智能计算详情，添加到timeoutDetails中（仅在调试模式下）
            ...(timeoutDetails && timeoutConfig.ENABLE_TIMEOUT_DEBUG && {
              smartCalculation: {
                chunkCount: timeoutDetails.chunkCount,
                complexityFactor: timeoutDetails.complexityFactor,
                adjustedChunkTimeout: Math.round(timeoutDetails.adjustedChunkTimeout / 1000),
                complexityReasons: timeoutDetails.complexityDetails
              }
            })
          }
        };

        return new Response(JSON.stringify(timeoutResponse), {
          status: 200,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 如果任务完成，返回音频文件URL
      if (taskStatus.status === 'complete') {
        // 优先使用R2直链，如果没有则回退到Worker代理下载
        const audioUrl = taskStatus.downloadUrl || `/api/tts/download/${taskId}`;

        if (env.DEBUG) {
          console.log(`[STATUS-CHECK] ✅ Task completed, returning download URL:`, {
            taskId: taskId,
            audioSize: taskStatus.audioSize,
            chunksProcessed: taskStatus.chunksProcessed,
            totalChunks: taskStatus.totalChunks,
            downloadMethod: taskStatus.downloadUrl ? 'R2_DIRECT' : 'WORKER_PROXY',
            audioUrl: audioUrl
          });
        } else {
          console.log(`[STATUS-CHECK] ✅ Task ${taskId} completed, returning download URL`);
        }
        return new Response(JSON.stringify({
          taskId: taskId,
          status: 'complete',
          audioUrl: audioUrl,
          audioSize: taskStatus.audioSize,
          chunksProcessed: taskStatus.chunksProcessed,
          totalChunks: taskStatus.totalChunks,
          completedAt: taskStatus.completedAt
        }), {
          status: 200,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 返回当前状态 - 包含详细进度信息
      if (env.DEBUG) {
        console.log(`[STATUS-CHECK] 📊 Returning current status:`, {
          taskId: taskId,
          status: taskStatus.status,
          currentStep: taskStatus.currentStep,
          hasError: !!taskStatus.error
        });
      }

      // 构建详细的状态响应
      const statusResponse = {
        taskId: taskId,
        status: taskStatus.status,
        createdAt: taskStatus.createdAt,
        ...(taskStatus.currentStep && { currentStep: taskStatus.currentStep }),
        ...(taskStatus.progress && { progress: taskStatus.progress }),
        ...(taskStatus.totalChunks && { totalChunks: taskStatus.totalChunks }),
        ...(taskStatus.chunksGenerated && { chunksGenerated: taskStatus.chunksGenerated }),
        ...(taskStatus.chunksProcessed && { chunksProcessed: taskStatus.chunksProcessed }),
        ...(taskStatus.audioSize && { audioSize: taskStatus.audioSize }),
        ...(taskStatus.error && { error: taskStatus.error }),
        ...(taskStatus.failedAtStep && { failedAtStep: taskStatus.failedAtStep })
      };

      return new Response(JSON.stringify(statusResponse), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error(`Error checking task status for ${taskId}:`, error);
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 处理R2音频文件列表请求 GET /api/tts/list-audios（调试用）
  if (request.method === 'GET' && request.url.includes('/list-audios')) {
    if (env.DEBUG) {
      console.log(`[R2-LIST] 🗂️ Listing audio files for debugging`);
    }

    try {
      const objects = await env.AUDIOS.list({ prefix: 'audios/' });

      const audioFiles = objects.objects.map(obj => ({
        key: obj.key,
        size: obj.size,
        uploaded: obj.uploaded,
        etag: obj.etag
      }));

      return new Response(JSON.stringify({
        success: true,
        message: 'Audio files listed successfully',
        files: audioFiles,
        totalFiles: audioFiles.length
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error(`[R2-LIST] ❌ Failed to list audio files:`, error);
      return new Response(JSON.stringify({
        success: false,
        message: 'Failed to list audio files',
        error: error.message
      }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 处理音频下载请求 GET /api/tts/download/{taskId}
  // 注意：此接口保留作为备用下载方式，新任务优先使用R2直链下载
  if (request.method === 'GET' && request.url.includes('/download/')) {
    try {
        // 直接调用新的、高效的下载处理器
        return await handleDownload(request, env, username);
    } catch (error) {
        console.error(`Error during download for request ${request.url}:`, error);
        return new Response(JSON.stringify({ error: 'Internal server error during download' }), {
            status: 500,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
    }
  }

  return new Response('Not Found', { status: 404, headers: corsHeaders() });
}

// 卡密套餐配置
const PACKAGES = {
  // --- 标准套餐 ---
  'M': { days: 30, price: 25, chars: 80000 },     // 月套餐，8万字符
  'Q': { days: 90, price: 55, chars: 250000 },    // 季度套餐，25万字符
  'H': { days: 180, price: 99, chars: 550000 },   // 半年套餐，55万字符

  // --- 新增：PRO套餐 ---
  'PM': { days: 30, price: 45, chars: 250000 },   // 月度PRO，25万字符
  'PQ': { days: 90, price: 120, chars: 800000 },  // 季度PRO，80万字符
  'PH': { days: 180, price: 220, chars: 2000000 }, // 半年PRO，200万字符

  // --- 特殊套餐 ---
  'PT': { days: 0.0208, price: 0, chars: 5000 }   // 30分钟测试套餐，5千字符
};

// 验证卡密
async function verifyCard(code, env) {
  const card = await env.CARDS.get(`card:${code}`);
  if (!card) return null;
  try {
    return JSON.parse(card);
  } catch (error) {
    return null;
  }
}

// 使用卡密
async function useCard(code, username, env) {
  const card = await verifyCard(code, env);

  if (!card) {
    throw new Error('无效的卡密');
  }

  if (card.s === 'used') {
    throw new Error('该卡密已被使用');
  }

  // 如果是测试套餐，检查是否已有其他有效套餐
  if (card.t === 'T') {
    const userData = JSON.parse(await env.USERS.get(`user:${username}`));
    if (userData.vip && Date.now() < userData.vip.expireAt && userData.vip.type !== 'T') {
      throw new Error('已有正式会员，无需使用测试套餐');
    }
  }

  // 先标记卡密为使用中
  card.s = 'using';
  await env.CARDS.put(`card:${code}`, JSON.stringify(card));

  try {
    const userData = JSON.parse(await env.USERS.get(`user:${username}`));

    // 获取新套餐的配置
    const newPackage = PACKAGES[card.t];
    if (!newPackage) {
      throw new Error('未知的套餐类型');
    }

    // 初始化VIP对象（如果不存在）
    if (!userData.vip) {
      userData.vip = {
        expireAt: 0,
        type: null,
        quotaChars: 0, // 新增：总配额
        usedChars: 0   // 新增：已用配额
      };
    } else {
      // 【核心修改】统一迁移逻辑
      // 如果用户的配额系统还未初始化 (无论是新用户还是老用户)
      if (userData.vip.quotaChars === undefined) {
        // 打印一条迁移日志，方便追踪
        console.log(`[MIGRATION] Migrating user ${username} to new quota system upon renewal.`);

        // 强制为该用户初始化配额系统，这是"单向阀门"
        userData.vip.quotaChars = 0;
        userData.vip.usedChars = 0;
      }
    }

    // 1. 计算新的到期时间
    const baseTime = Math.max(userData.vip.expireAt || 0, Date.now());
    userData.vip.expireAt = baseTime + (newPackage.days * 86400000);

    // 2. 【核心修改】叠加字符数配额（现在对所有用户生效）
    if (userData.vip.quotaChars !== undefined) {
      // 这段代码现在对所有续费用户都生效
      const isExpired = Date.now() > (userData.vip.expireAt || 0);

      // 如果会员已过期，则不保留剩余字符；否则，保留剩余字符
      const oldRemainingChars = isExpired ? 0 : Math.max(0, userData.vip.quotaChars - userData.vip.usedChars);

      // 新的总配额 = 剩余配额 + 新套餐配额
      userData.vip.quotaChars = oldRemainingChars + newPackage.chars;

      // 已用配额清零
      userData.vip.usedChars = 0;

      console.log(`[CARD-USE] Updated quota for user ${username}: ${userData.vip.quotaChars} chars`);
    }
    // 【重要】删除了原来的 else 分支！

    // 3. 更新套餐类型
    userData.vip.type = card.t;

    // 标记卡密为已使用
    card.s = 'used';
    card.u = username;
    card.a = Date.now();

    // 保存更新
    await env.USERS.put(`user:${username}`, JSON.stringify(userData));
    await env.CARDS.put(`card:${code}`, JSON.stringify(card));

    return userData.vip;
  } catch (error) {
    // 如果出错，恢复卡密状态
    card.s = 'unused';
    await env.CARDS.put(`card:${code}`, JSON.stringify(card));
    throw error;
  }
}

// 添加预览音频处理路由
async function handlePreview(request, env) {
  const url = new URL(request.url);
  const filename = url.pathname.split('/').pop();

  if (!filename) {
    return new Response('File not found', {
      status: 404,
      headers: corsHeaders()
    });
  }

  try {
    // 从 R2 存储桶获取音频文件
    const object = await env.AUDIO_BUCKET.get(`preview-audio-kf/${filename}`);

    if (!object) {
      return new Response('File not found', {
        status: 404,
        headers: corsHeaders()
      });
    }

    // 返回音频文件
    return new Response(object.body, {
      headers: {
        ...corsHeaders(),
        'Content-Type': 'audio/mpeg',
        'Content-Length': object.size,
        'Cache-Control': 'public, max-age=31536000'
      }
    });
  } catch (error) {
    console.error('Error fetching preview audio:', error);
    return new Response('Internal Server Error', {
      status: 500,
      headers: corsHeaders()
    });
  }
}

// ========== 主处理函数 ==========
// 【ES Modules格式】删除了addEventListener，改用export default作为唯一入口

async function handleRequest(request, env, event = null) {
  // 处理 CORS 预检请求
  if (request.method === 'OPTIONS') {
    return handleOptions(request);
  }

  const url = new URL(request.url);

  // 处理预览音频请求
  if (url.pathname.startsWith('/preview-audio-kf/')) {
    const audioName = url.pathname.split('/preview-audio-kf/')[1];
    // 从 R2 获取音频文件
    const object = await env.AUDIO_BUCKET.get(audioName);

    if (object === null) {
      return new Response('Audio not found', {
        status: 404,
        headers: corsHeaders()
      });
    }

    const headers = new Headers();
    headers.set('Content-Type', 'audio/mpeg');
    headers.set('Cache-Control', 'public, max-age=31536000');
    // 添加 CORS 头
    Object.entries(corsHeaders()).forEach(([key, value]) => {
      headers.set(key, value);
    });

    return new Response(object.body, { headers });
  }

  // --- START: WebSocket 路由处理 (高优先级) ---

  // 【修复】单人 TTS WebSocket 连接请求 - 使用精确匹配
  if (url.pathname === '/api/tts/ws/generate') {
    // 【关键改动】为每个任务创建一个唯一的 DO ID
    // 这个 ID 之后会成为任务的 taskId
    const taskId = generateUUID();
    const doId = env.TTS_TASK_DO.idFromName(taskId);

    // 【新增】支持排除特定数据中心的位置提示
    // 从查询参数中获取要排除的位置列表
    const excludeLocations = url.searchParams.get('excludeLocations')?.split(',') || [];
    const locationHint = getRandomLocationHint(env, excludeLocations);

    // 【新增】使用 Analytics Engine 记录DO创建意图
    try {
      if (env.DO_ANALYTICS) {
        env.DO_ANALYTICS.writeDataPoint({
          blobs: [
            taskId,                           // blob_0: taskId
            "creation_intent",                // blob_1: eventType
            "single_tts",                     // blob_2: taskType
            locationHint || "auto",           // blob_3: locationHint
            "N/A",                            // blob_4: actualColo (此时还未知)
            request.cf?.country || "unknown"  // blob_5: country
          ],
          doubles: [1],
          indexes: [taskId]  // 【修复】只使用一个索引，符合免费计划限制
        });
        if (env.DEBUG) {
          console.log(`[ANALYTICS] Recorded creation intent for single TTS task ${taskId}, locationHint: ${locationHint || "auto"}`);
        }
      }
    } catch (analyticsError) {
      if (env.DEBUG) {
        console.error('[ANALYTICS] Failed to record creation intent:', analyticsError);
      }
      // 不影响主流程，继续执行
    }

    let stub;
    try {
      stub = locationHint
        ? env.TTS_TASK_DO.get(doId, { locationHint })
        : env.TTS_TASK_DO.get(doId);

      if (env.DEBUG && locationHint) {
        console.log(`[DO-ROUTING] Single TTS task ${taskId} assigned to location: ${locationHint}${excludeLocations.length > 0 ? ` (excluded: [${excludeLocations.join(', ')}])` : ''}`);
      }
    } catch (error) {
      // 【修复】如果locationHint不支持，回退到不使用locationHint
      if (error.message?.includes('unsupported locationHint')) {
        console.warn(`[DO-ROUTING] LocationHint ${locationHint} not supported, falling back to default routing`);
        stub = env.TTS_TASK_DO.get(doId);
      } else {
        throw error;
      }
    }

    // 【简化】直接将原始请求转发给 DO，不需要任何检查
    // DO 的 fetch 方法会处理 Upgrade header 的检查
    return stub.fetch(request);
  }

  // 【修复】多人对话 WebSocket 连接请求 - 移到这里确保优先处理
  if (url.pathname === '/api/tts/ws/dialogue/generate') {
    const dialogueTaskId = generateUUID();
    const doId = env.TTS_TASK_DO.idFromName(dialogueTaskId);

    // 【新增】支持排除特定数据中心的位置提示
    // 从查询参数中获取要排除的位置列表
    const excludeLocations = url.searchParams.get('excludeLocations')?.split(',') || [];
    const locationHint = getRandomLocationHint(env, excludeLocations);

    // 【新增】使用 Analytics Engine 记录DO创建意图
    try {
      if (env.DO_ANALYTICS) {
        env.DO_ANALYTICS.writeDataPoint({
          blobs: [
            dialogueTaskId,                   // blob_0: taskId
            "creation_intent",                // blob_1: eventType
            "dialogue_tts",                   // blob_2: taskType
            locationHint || "auto",           // blob_3: locationHint
            "N/A",                            // blob_4: actualColo (此时还未知)
            request.cf?.country || "unknown"  // blob_5: country
          ],
          doubles: [1],
          indexes: [dialogueTaskId]  // 【修复】只使用一个索引，符合免费计划限制
        });
        if (env.DEBUG) {
          console.log(`[ANALYTICS] Recorded creation intent for dialogue TTS task ${dialogueTaskId}, locationHint: ${locationHint || "auto"}`);
        }
      }
    } catch (analyticsError) {
      if (env.DEBUG) {
        console.error('[ANALYTICS] Failed to record creation intent:', analyticsError);
      }
      // 不影响主流程，继续执行
    }

    let stub;
    try {
      stub = locationHint
        ? env.TTS_TASK_DO.get(doId, { locationHint })
        : env.TTS_TASK_DO.get(doId);

      if (env.DEBUG && locationHint) {
        console.log(`[DO-ROUTING] Dialogue TTS task ${dialogueTaskId} assigned to location: ${locationHint}${excludeLocations.length > 0 ? ` (excluded: [${excludeLocations.join(', ')}])` : ''}`);
      }
    } catch (error) {
      // 【修复】如果locationHint不支持，回退到不使用locationHint
      if (error.message?.includes('unsupported locationHint')) {
        console.warn(`[DO-ROUTING] LocationHint ${locationHint} not supported, falling back to default routing`);
        stub = env.TTS_TASK_DO.get(doId);
      } else {
        throw error;
      }
    }

    return stub.fetch(request);
  }

  // --- END: WebSocket 路由处理 ---

  // --- START: HTTP API 路由处理 (低优先级) ---

  // 其他原有的路由处理...
  if (url.pathname.startsWith('/api/auth/')) {
    const response = await handleAuth(request, env);
    const headers = {...corsHeaders(), ...response.headers};
    return new Response(response.body, {
      status: response.status,
      headers: headers
    });
  }

  // 【新增】自动标注API路由处理
  if (url.pathname.startsWith('/api/auto-tag/')) {
    const response = await handleAutoTag(request, env);
    const headers = {...corsHeaders(), ...response.headers};
    return new Response(response.body, {
      status: response.status,
      headers: headers
    });
  }

  // 【重要】保留旧的/api/tts/路由，但主要是为了查询历史任务状态
  // 【修复】现在不会错误拦截 WebSocket 请求了
  if (url.pathname.startsWith('/api/tts/')) {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    if (!token) {
        return new Response(JSON.stringify({
          error: 'Unauthorized',
          code: 'NO_TOKEN'
        }), {
          status: 401,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
    }

    try {
        const username = await verifyToken(token, env);


        // 保留 handleTTS 以处理 /status 和 /download
        const response = await handleTTS(request, username, env);
        const headers = {...corsHeaders(), ...response.headers};
        return new Response(response.body, {
            status: response.status,
            headers: headers
        });
    } catch (error) {
        if (error.cause === 'quota') {
          return new Response(JSON.stringify({ error: error.message, type: 'quota' }), {
            status: 403,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        }
        // 【关键修改】区分认证错误，使用统一的认证错误处理函数
        if (isAuthError(error)) {
          return createAuthErrorResponse(error);
        }
        // 其他未知错误保持原有处理
        return new Response(JSON.stringify({ error: 'Unauthorized' }), {
          status: 401,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
    }
  }

  if (url.pathname === '/api/card/use') {
    const { code } = await request.json();
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        code: 'NO_TOKEN'
      }), {
        status: 401,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }

    try {
      // 【新增】检查是否启用主后端API模式
      if (isMainBackendEnabled(env)) {
        // 使用主后端API激活卡密
        const result = await useCardViaMainBackend(code, token, env);

        if (result.success) {
          return new Response(JSON.stringify({
            quota: result.quota
          }), {
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        } else {
          return new Response(JSON.stringify({ error: result.error }), {
            status: 400,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        }
      }

      // 【保留】原有的KV存储逻辑（作为降级方案）
      const username = await verifyToken(token, env);
      const quota = await useCard(code, username, env);
      return new Response(JSON.stringify({ quota }), {
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      // 区分认证错误和业务错误
      if (isAuthError(error)) {
        // 认证相关错误，使用统一的认证错误处理
        return createAuthErrorResponse(error);
      } else {
        // 业务逻辑错误（如卡密无效等），保持原有的400状态码
        return new Response(JSON.stringify({ error: error.message }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }
    }
  }

  if (url.pathname === '/api/user/quota') {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        code: 'NO_TOKEN'
      }), {
        status: 401,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }

    try {
      // 【新增】检查是否启用主后端API模式
      if (isMainBackendEnabled(env)) {
        // 使用主后端API获取配额信息
        const result = await getUserQuotaViaMainBackend(token, env);

        if (result.success) {
          // 【修改】将嵌套结构转换为前端期望的扁平结构
          const nestedResponse = {
            username: result.username,
            vip: result.vip,
            usage: result.usage
          };
          const flatResponse = convertToFlatResponse(nestedResponse);

          return new Response(JSON.stringify(flatResponse), {
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        } else {
          return new Response(JSON.stringify({ error: result.error }), {
            status: 400,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        }
      }

      // 【保留】原有的KV存储逻辑（作为降级方案）
      const username = await verifyToken(token, env);
      const userData = JSON.parse(await env.USERS.get(`user:${username}`));
      const vip = userData.vip || { expireAt: 0 };

      // 计算配额详细信息
      const quotaDetails = calculateQuotaDetails(userData);

      // 【修改】构建嵌套结构然后转换为扁平结构
      const nestedResponse = {
        username: username,
        vip: {
          type: vip.type,
          expireAt: vip.expireAt,
          quotaChars: quotaDetails.quotaChars,
          usedChars: quotaDetails.usedChars,
          remainingChars: quotaDetails.remainingChars,
          usagePercentage: quotaDetails.usagePercentage,
          isLegacyUser: quotaDetails.isLegacyUser,
          isExpired: Date.now() > vip.expireAt
        },
        usage: userData.usage || {
          totalChars: 0,
          monthlyChars: 0,
          monthlyResetAt: getNextMonthResetTimestamp()
        }
      };

      // 【新增】转换为前端期望的扁平结构
      const flatResponse = convertToFlatResponse(nestedResponse);

      return new Response(JSON.stringify(flatResponse), {
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      // 【关键修改】使用统一的认证错误处理函数
      return createAuthErrorResponse(error);
    }
  }

  // 【新增】用户字符数用量查询接口
  if (url.pathname === '/api/user/usage') {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        code: 'NO_TOKEN'
      }), {
        status: 401,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }

    try {
      const username = await verifyToken(token, env);
      const userDataString = await env.USERS.get(`user:${username}`);
      if (!userDataString) {
        return new Response(JSON.stringify({ error: 'User not found' }), {
          status: 404,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const userData = JSON.parse(userDataString);

      // 确保老用户也能正确返回（向后兼容）
      const usage = userData.usage || {
        totalChars: 0,
        monthlyChars: 0,
        monthlyResetAt: getNextMonthResetTimestamp()
      };

      // 检查月度重置
      if (Date.now() >= usage.monthlyResetAt) {
        usage.monthlyChars = 0;
        usage.monthlyResetAt = getNextMonthResetTimestamp();

        // 如果需要重置，更新用户数据
        userData.usage = usage;
        await env.USERS.put(`user:${username}`, JSON.stringify(userData));
      }

      return new Response(JSON.stringify(usage), {
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      return createAuthErrorResponse(error);
    }
  }

  // 【新增】管理员用量查询接口
  if (url.pathname === '/api/admin/users/usage') {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        code: 'NO_TOKEN'
      }), {
        status: 401,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }

    try {
      // 验证token并获取用户名
      const username = await verifyToken(token, env);

      // 检查管理员权限
      await checkAdminPermission(username, env);

      // 获取查询参数
      const limit = Math.min(parseInt(url.searchParams.get('limit')) || 100, 500); // 最大500
      const cursor = url.searchParams.get('cursor') || null;

      console.log(`[ADMIN-API] Admin ${username} requesting users usage. Limit: ${limit}, Cursor: ${cursor || 'null'}`);

      // 获取所有用户用量数据
      const result = await getAllUsersUsage(env, limit, cursor);

      console.log(`[ADMIN-API] Successfully returned ${result.users.length} users usage data to admin ${username}`);

      return new Response(JSON.stringify(result), {
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('[ADMIN-API] Error in admin users usage endpoint:', error);

      // 区分权限错误和其他错误
      if (error.message.includes('管理员') || error.message.includes('权限') || error.message.includes('配置')) {
        return new Response(JSON.stringify({
          error: error.message,
          code: 'ADMIN_PERMISSION_DENIED'
        }), {
          status: 403,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 认证错误
      if (isAuthError(error)) {
        return createAuthErrorResponse(error);
      }

      // 其他服务器错误
      return new Response(JSON.stringify({
        error: '服务器内部错误',
        code: 'INTERNAL_ERROR'
      }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // --- END: HTTP API 路由处理 ---

  return new Response('Not Found', {
    status: 404,
    headers: corsHeaders()
  });
}

// 【ES Modules格式】这是整个文件的唯一入口点
export default {
  fetch(request, env, ctx) {
    // ctx包含waitUntil方法，用于异步任务处理
    // handleRequest已经是async函数，会返回Promise
    return handleRequest(request, env, ctx);
  }
};

/**
 * 【新增】合并多个音频 ArrayBuffer
 * @param {Array<ArrayBuffer>} audioDataList - 包含多个 ArrayBuffer 的数组
 * @returns {ArrayBuffer} - 合并后的单个 ArrayBuffer
 */
function combineAudio(audioDataList) {
  if (!audioDataList || audioDataList.length === 0) {
    return new ArrayBuffer(0);
  }
  if (audioDataList.length === 1) {
    return audioDataList[0];
  }

  const totalLength = audioDataList.reduce((acc, buffer) => acc + (buffer.byteLength || 0), 0);
  const combined = new Uint8Array(totalLength);

  let offset = 0;
  for (const buffer of audioDataList) {
    if (buffer && buffer.byteLength > 0) {
      combined.set(new Uint8Array(buffer), offset);
      offset += buffer.byteLength;
    }
  }

  return combined.buffer;
}

// ========== 自动标注功能实现 ==========

/**
 * 获取自动标注配置
 * @param {object} env - 环境变量
 * @returns {object} 自动标注配置
 */
function getAutoTagConfig(env) {
  return {
    API_URL: env.AUTO_TAG_API_URL || 'https://geminitts.aispeak.top/api/tts/process',
    TOKEN: env.AUTO_TAG_TOKEN || '',
    TIMEOUT: parseInt(env.AUTO_TAG_TIMEOUT || '30000'),
    RATE_LIMIT: parseInt(env.AUTO_TAG_RATE_LIMIT || '10'),
    RATE_WINDOW_MINUTES: 1 // 频率限制时间窗口（分钟）
  };
}

/**
 * 检查用户的自动标注频率限制
 * @param {string} username - 用户名
 * @param {object} env - 环境变量
 * @returns {Promise<{allowed: boolean, remaining: number, resetTime: number}>}
 */
async function checkAutoTagRateLimit(username, env) {
  const config = getAutoTagConfig(env);
  const now = Date.now();
  const windowStart = Math.floor(now / (config.RATE_WINDOW_MINUTES * 60 * 1000)) * (config.RATE_WINDOW_MINUTES * 60 * 1000);
  const resetTime = windowStart + (config.RATE_WINDOW_MINUTES * 60 * 1000);

  const rateLimitKey = `auto_tag_rate:${username}:${windowStart}`;

  try {
    const currentCountStr = await env.USERS.get(rateLimitKey);
    const currentCount = currentCountStr ? parseInt(currentCountStr) : 0;

    if (currentCount >= config.RATE_LIMIT) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: resetTime
      };
    }

    // 增加计数
    await env.USERS.put(rateLimitKey, String(currentCount + 1), {
      expirationTtl: config.RATE_WINDOW_MINUTES * 60 + 10 // 多10秒缓冲
    });

    return {
      allowed: true,
      remaining: config.RATE_LIMIT - currentCount - 1,
      resetTime: resetTime
    };
  } catch (error) {
    console.error('[AUTO-TAG] Rate limit check failed:', error);
    // 如果检查失败，允许请求但设置较低的剩余次数
    return {
      allowed: true,
      remaining: 1,
      resetTime: resetTime
    };
  }
}

/**
 * 调用外部自动标注API
 * @param {string} text - 要处理的文本
 * @param {string} language - 语言设置
 * @param {object} env - 环境变量
 * @returns {Promise<string>} 处理后的文本
 */
async function callAutoTagAPI(text, language, env) {
  const config = getAutoTagConfig(env);

  if (!config.TOKEN) {
    throw new Error('自动标注服务未配置API令牌');
  }

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), config.TIMEOUT);

  try {
    const response = await fetch(config.API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.TOKEN}`
      },
      body: JSON.stringify({
        text: text,
        language: language || 'auto'
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('[AUTO-TAG] External API error:', response.status, errorText);
      throw new Error(`外部标注服务错误: ${response.status}`);
    }

    const result = await response.json();

    // 根据外部API的响应格式提取处理后的文本
    // 这里假设外部API返回 { processedText: "..." } 格式
    if (result.processedText) {
      return result.processedText;
    } else if (result.text) {
      return result.text;
    } else if (typeof result === 'string') {
      return result;
    } else {
      console.warn('[AUTO-TAG] Unexpected API response format:', result);
      return text; // 如果格式不符合预期，返回原文本
    }
  } catch (error) {
    clearTimeout(timeoutId);

    if (error.name === 'AbortError') {
      throw new Error('请求超时，请稍后重试');
    }

    console.error('[AUTO-TAG] API call failed:', error);
    throw error;
  }
}

/**
 * 处理自动标注请求的主函数
 * @param {Request} request - HTTP请求对象
 * @param {object} env - 环境变量
 * @returns {Promise<Response>} HTTP响应
 */
async function handleAutoTag(request, env) {
  const url = new URL(request.url);

  try {
    // 1. 处理 POST /api/auto-tag/process - 文本处理接口
    if (request.method === 'POST' && url.pathname === '/api/auto-tag/process') {
      return await handleAutoTagProcess(request, env);
    }

    // 2. 处理 GET /api/auto-tag/status - 使用状态查询接口
    if (request.method === 'GET' && url.pathname === '/api/auto-tag/status') {
      return await handleAutoTagStatus(request, env);
    }

    // 3. 处理 GET /api/auto-tag/admin/stats - 管理员统计接口
    if (request.method === 'GET' && url.pathname === '/api/auto-tag/admin/stats') {
      return await handleAutoTagAdminStats(request, env);
    }

    // 4. 不支持的路径
    return new Response(JSON.stringify({
      error: '不支持的API路径',
      code: 'UNSUPPORTED_ENDPOINT'
    }), {
      status: 404,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('[AUTO-TAG] Handler error:', error);

    // 统一错误处理
    if (isAuthError(error)) {
      return createAuthErrorResponse(error);
    }

    return new Response(JSON.stringify({
      error: '服务器内部错误',
      code: 'INTERNAL_ERROR'
    }), {
      status: 500,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理自动标注文本处理请求
 * @param {Request} request - HTTP请求对象
 * @param {object} env - 环境变量
 * @returns {Promise<Response>} HTTP响应
 */
async function handleAutoTagProcess(request, env) {
  // 1. 认证检查
  const token = request.headers.get('Authorization')?.replace('Bearer ', '');
  if (!token) {
    return new Response(JSON.stringify({
      error: '需要提供访问令牌',
      code: 'TOKEN_REQUIRED'
    }), {
      status: 401,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  let username;
  try {
    username = await verifyToken(token, env);
  } catch (error) {
    return createAuthErrorResponse(error);
  }

  // 2. 解析请求体
  let requestData;
  try {
    requestData = await request.json();
  } catch (error) {
    return new Response(JSON.stringify({
      error: '请求格式错误',
      code: 'INVALID_JSON'
    }), {
      status: 400,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  const { text, language = 'auto' } = requestData;

  // 3. 输入验证
  if (!text || typeof text !== 'string') {
    return new Response(JSON.stringify({
      error: '请提供要处理的文本内容',
      code: 'INVALID_INPUT'
    }), {
      status: 400,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  if (text.length > 5000) {
    return new Response(JSON.stringify({
      error: '文本长度不能超过5000字符',
      code: 'TEXT_TOO_LONG'
    }), {
      status: 400,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  // 4. VIP权限检查
  try {
    await checkVip(username, env, 'STANDARD', 0); // 不检查字符配额，只检查会员状态
  } catch (error) {
    if (error.cause === 'quota') {
      const errorMessage = error.message;
      let errorCode = 'VIP_REQUIRED';

      if (errorMessage.includes('过期')) {
        errorCode = 'VIP_EXPIRED';
      }

      return new Response(JSON.stringify({
        error: errorMessage,
        code: errorCode
      }), {
        status: 403,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
    throw error;
  }

  // 5. 频率限制检查
  const rateLimitResult = await checkAutoTagRateLimit(username, env);
  if (!rateLimitResult.allowed) {
    return new Response(JSON.stringify({
      error: '请求过于频繁，请稍后重试',
      code: 'RATE_LIMIT_EXCEEDED',
      rateLimit: {
        remaining: rateLimitResult.remaining,
        resetTime: rateLimitResult.resetTime
      }
    }), {
      status: 429,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  // 6. 调用外部API处理文本
  const startTime = Date.now();
  let processedText;

  try {
    processedText = await callAutoTagAPI(text, language, env);
  } catch (error) {
    console.error('[AUTO-TAG] External API call failed:', error);

    // 记录失败的审计日志
    await logAutoTagUsage(username, text.length, 0, false, error.message, Date.now() - startTime, env);

    if (error.message.includes('超时')) {
      return new Response(JSON.stringify({
        error: '请求超时，请稍后重试',
        code: 'REQUEST_TIMEOUT'
      }), {
        status: 504,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      error: '服务暂时不可用，请稍后重试',
      code: 'SERVICE_UNAVAILABLE'
    }), {
      status: 500,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  const processingTime = Date.now() - startTime;

  // 7. 记录成功的审计日志
  await logAutoTagUsage(username, text.length, processedText.length, true, null, processingTime, env);

  // 8. 返回成功响应
  return new Response(JSON.stringify({
    success: true,
    processedText: processedText,
    originalLength: text.length,
    processedLength: processedText.length,
    rateLimit: {
      remaining: rateLimitResult.remaining,
      resetTime: rateLimitResult.resetTime
    }
  }), {
    status: 200,
    headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
  });
}

/**
 * 记录自动标注使用情况的审计日志
 * @param {string} username - 用户名
 * @param {number} originalLength - 原始文本长度
 * @param {number} processedLength - 处理后文本长度
 * @param {boolean} success - 是否成功
 * @param {string|null} error - 错误信息
 * @param {number} processingTime - 处理时间（毫秒）
 * @param {object} env - 环境变量
 */
async function logAutoTagUsage(username, originalLength, processedLength, success, error, processingTime, env) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    username,
    textLength: originalLength,
    processedLength,
    success,
    error,
    processingTime
  };

  try {
    // 使用KV存储记录日志，按日期分组
    const dateKey = timestamp.split('T')[0]; // YYYY-MM-DD格式
    const logKey = `auto_tag_log:${dateKey}:${username}:${Date.now()}`;

    await env.USERS.put(logKey, JSON.stringify(logEntry), {
      expirationTtl: 30 * 24 * 60 * 60 // 30天过期
    });

    // 更新用户的使用统计
    await updateAutoTagStats(username, originalLength, success, env);

    console.log('[AUTO-TAG] Usage logged:', {
      username,
      success,
      textLength: originalLength,
      processingTime
    });
  } catch (logError) {
    console.error('[AUTO-TAG] Failed to log usage:', logError);
    // 日志记录失败不应影响主要功能
  }
}

/**
 * 更新用户的自动标注使用统计
 * @param {string} username - 用户名
 * @param {number} textLength - 文本长度
 * @param {boolean} success - 是否成功
 * @param {object} env - 环境变量
 */
async function updateAutoTagStats(username, textLength, success, env) {
  try {
    const statsKey = `auto_tag_stats:${username}`;
    const existingStatsStr = await env.USERS.get(statsKey);

    let stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalTextLength: 0,
      lastUsed: new Date().toISOString().split('T')[0]
    };

    if (existingStatsStr) {
      stats = { ...stats, ...JSON.parse(existingStatsStr) };
    }

    // 更新统计数据
    stats.totalRequests += 1;
    if (success) {
      stats.successfulRequests += 1;
      stats.totalTextLength += textLength;
    } else {
      stats.failedRequests += 1;
    }
    stats.lastUsed = new Date().toISOString().split('T')[0];

    await env.USERS.put(statsKey, JSON.stringify(stats), {
      expirationTtl: 365 * 24 * 60 * 60 // 1年过期
    });
  } catch (error) {
    console.error('[AUTO-TAG] Failed to update stats:', error);
  }
}

/**
 * 处理自动标注状态查询请求
 * @param {Request} request - HTTP请求对象
 * @param {object} env - 环境变量
 * @returns {Promise<Response>} HTTP响应
 */
async function handleAutoTagStatus(request, env) {
  // 1. 认证检查
  const token = request.headers.get('Authorization')?.replace('Bearer ', '');
  if (!token) {
    return new Response(JSON.stringify({
      error: '需要提供访问令牌',
      code: 'TOKEN_REQUIRED'
    }), {
      status: 401,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  let username;
  try {
    username = await verifyToken(token, env);
  } catch (error) {
    return createAuthErrorResponse(error);
  }

  try {
    // 2. 获取当前频率限制状态
    const config = getAutoTagConfig(env);
    const now = Date.now();
    const windowStart = Math.floor(now / (config.RATE_WINDOW_MINUTES * 60 * 1000)) * (config.RATE_WINDOW_MINUTES * 60 * 1000);
    const resetTime = windowStart + (config.RATE_WINDOW_MINUTES * 60 * 1000);
    const rateLimitKey = `auto_tag_rate:${username}:${windowStart}`;

    const currentCountStr = await env.USERS.get(rateLimitKey);
    const currentCount = currentCountStr ? parseInt(currentCountStr) : 0;
    const remaining = Math.max(0, config.RATE_LIMIT - currentCount);

    // 3. 获取用户使用统计
    const statsKey = `auto_tag_stats:${username}`;
    const statsStr = await env.USERS.get(statsKey);

    let usage = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalTextLength: 0,
      lastUsed: null,
      dailyBreakdown: []
    };

    if (statsStr) {
      const stats = JSON.parse(statsStr);
      usage = {
        totalRequests: stats.totalRequests || 0,
        successfulRequests: stats.successfulRequests || 0,
        failedRequests: stats.failedRequests || 0,
        totalTextLength: stats.totalTextLength || 0,
        lastUsed: stats.lastUsed || null,
        dailyBreakdown: [] // 简化实现，不提供详细的日分解
      };
    }

    // 4. 返回状态信息
    return new Response(JSON.stringify({
      rateLimit: {
        maxRequests: config.RATE_LIMIT,
        remaining: remaining,
        resetTime: resetTime,
        windowMinutes: config.RATE_WINDOW_MINUTES
      },
      usage: usage
    }), {
      status: 200,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('[AUTO-TAG] Status query failed:', error);
    return new Response(JSON.stringify({
      error: '查询状态失败',
      code: 'STATUS_QUERY_FAILED'
    }), {
      status: 500,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理自动标注管理员统计请求
 * @param {Request} request - HTTP请求对象
 * @param {object} env - 环境变量
 * @returns {Promise<Response>} HTTP响应
 */
async function handleAutoTagAdminStats(request, env) {
  // 1. 认证检查
  const token = request.headers.get('Authorization')?.replace('Bearer ', '');
  if (!token) {
    return new Response(JSON.stringify({
      error: '需要提供访问令牌',
      code: 'TOKEN_REQUIRED'
    }), {
      status: 401,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  let username;
  try {
    username = await verifyToken(token, env);
  } catch (error) {
    return createAuthErrorResponse(error);
  }

  // 2. 管理员权限检查
  try {
    await checkAdminPermission(username, env);
  } catch (error) {
    return new Response(JSON.stringify({
      error: error.message,
      code: 'ADMIN_PERMISSION_DENIED'
    }), {
      status: 403,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  try {
    // 3. 获取查询参数
    const url = new URL(request.url);
    const dateParam = url.searchParams.get('date');
    const targetDate = dateParam || new Date().toISOString().split('T')[0];

    // 4. 简化的统计实现（由于KV存储的限制，这里提供基础统计）
    // 在实际生产环境中，可能需要更复杂的数据聚合逻辑
    const stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      uniqueUsers: 0,
      totalTextLength: 0,
      averageProcessingTime: 0
    };

    // 注意：由于KV存储的查询限制，这里返回模拟数据
    // 在实际实现中，可能需要使用Durable Objects或其他方案来聚合统计数据
    console.log(`[AUTO-TAG] Admin stats requested for date: ${targetDate} by ${username}`);

    return new Response(JSON.stringify({
      date: targetDate,
      stats: stats
    }), {
      status: 200,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('[AUTO-TAG] Admin stats query failed:', error);
    return new Response(JSON.stringify({
      error: '查询统计数据失败',
      code: 'STATS_QUERY_FAILED'
    }), {
      status: 500,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }
}